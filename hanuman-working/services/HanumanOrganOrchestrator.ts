import { EventEmitter } from 'events';
import { AgentConnectionManager, AgentConfig, AgentMessage, AgentResponse } from './AgentConnectionManager';
import { TrimurtiController, CosmicEnergy, CosmicPhase, CosmicContext } from './TrimurtiController';

// Types pour l'orchestration des organes
export interface OrganState {
  id: string;
  name: string;
  type: 'sensory' | 'motor' | 'cognitive' | 'emotional';
  status: 'active' | 'inactive' | 'processing' | 'error';
  connectedAgents: string[];
  lastActivity: Date;
  metrics: {
    activityLevel: number;
    responseTime: number;
    successRate: number;
    dataProcessed: number;
  };
}

export interface SynapticConnection {
  id: string;
  fromOrgan: string;
  toOrgan: string;
  strength: number;
  type: 'excitatory' | 'inhibitory' | 'modulatory';
  lastActivation: Date;
  activationCount: number;
}

export interface NeuralSignal {
  id: string;
  source: string;
  target: string;
  type: string;
  data: any;
  timestamp: Date;
  priority: 'low' | 'medium' | 'high' | 'critical';
  processed: boolean;
}

/**
 * Orchestrateur central des organes d'Hanuman
 * Coordonne les organes sensoriels avec l'architecture neuronale distribuée
 */
export class HanumanOrganOrchestrator extends EventEmitter {
  private agentManager: AgentConnectionManager;
  private trimurtiController: TrimurtiController;
  private organs: Map<string, OrganState> = new Map();
  private synapticConnections: Map<string, SynapticConnection> = new Map();
  private neuralSignals: NeuralSignal[] = [];
  private processingQueue: NeuralSignal[] = [];
  private isProcessing = false;
  private cosmicAlignment = 0.85;

  constructor(agentManager: AgentConnectionManager) {
    super();
    this.agentManager = agentManager;
    this.trimurtiController = new TrimurtiController();
    this.initializeOrgans();
    this.setupSynapticConnections();
    this.startNeuralProcessing();
    this.bindAgentEvents();
    this.bindCosmicEvents();
  }

  /**
   * Initialise les organes d'Hanuman
   */
  private initializeOrgans(): void {
    const organConfigs: OrganState[] = [
      {
        id: 'vision',
        name: 'Vision d\'Hanuman',
        type: 'sensory',
        status: 'inactive',
        connectedAgents: ['agent-web-research'],
        lastActivity: new Date(),
        metrics: {
          activityLevel: 0,
          responseTime: 0,
          successRate: 100,
          dataProcessed: 0
        }
      },
      {
        id: 'hearing',
        name: 'Ouïe d\'Hanuman',
        type: 'sensory',
        status: 'inactive',
        connectedAgents: ['agent-performance', 'agent-backend'],
        lastActivity: new Date(),
        metrics: {
          activityLevel: 0,
          responseTime: 0,
          successRate: 100,
          dataProcessed: 0
        }
      },
      {
        id: 'touch',
        name: 'Toucher d\'Hanuman',
        type: 'sensory',
        status: 'inactive',
        connectedAgents: ['agent-devops', 'agent-backend', 'agent-security'],
        lastActivity: new Date(),
        metrics: {
          activityLevel: 0,
          responseTime: 0,
          successRate: 100,
          dataProcessed: 0
        }
      },
      {
        id: 'broca',
        name: 'Aire de Broca',
        type: 'cognitive',
        status: 'inactive',
        connectedAgents: ['agent-frontend', 'agent-marketing'],
        lastActivity: new Date(),
        metrics: {
          activityLevel: 0,
          responseTime: 0,
          successRate: 100,
          dataProcessed: 0
        }
      },
      {
        id: 'wernicke',
        name: 'Aire de Wernicke',
        type: 'cognitive',
        status: 'inactive',
        connectedAgents: ['agent-documentation'],
        lastActivity: new Date(),
        metrics: {
          activityLevel: 0,
          responseTime: 0,
          successRate: 100,
          dataProcessed: 0
        }
      },
      {
        id: 'motor-cortex',
        name: 'Cortex Moteur',
        type: 'motor',
        status: 'inactive',
        connectedAgents: ['agent-devops', 'agent-qa'],
        lastActivity: new Date(),
        metrics: {
          activityLevel: 0,
          responseTime: 0,
          successRate: 100,
          dataProcessed: 0
        }
      }
    ];

    organConfigs.forEach(organ => {
      this.organs.set(organ.id, organ);
    });

    console.log(`🧠 Initialized ${organConfigs.length} organs in Hanuman's body`);
  }

  /**
   * Configure les connexions synaptiques entre organes
   */
  private setupSynapticConnections(): void {
    const connections: SynapticConnection[] = [
      {
        id: 'vision-broca',
        fromOrgan: 'vision',
        toOrgan: 'broca',
        strength: 0.8,
        type: 'excitatory',
        lastActivation: new Date(),
        activationCount: 0
      },
      {
        id: 'vision-wernicke',
        fromOrgan: 'vision',
        toOrgan: 'wernicke',
        strength: 0.7,
        type: 'excitatory',
        lastActivation: new Date(),
        activationCount: 0
      },
      {
        id: 'hearing-touch',
        fromOrgan: 'hearing',
        toOrgan: 'touch',
        strength: 0.6,
        type: 'modulatory',
        lastActivation: new Date(),
        activationCount: 0
      },
      {
        id: 'broca-motor',
        fromOrgan: 'broca',
        toOrgan: 'motor-cortex',
        strength: 0.9,
        type: 'excitatory',
        lastActivation: new Date(),
        activationCount: 0
      },
      {
        id: 'wernicke-broca',
        fromOrgan: 'wernicke',
        toOrgan: 'broca',
        strength: 0.85,
        type: 'excitatory',
        lastActivation: new Date(),
        activationCount: 0
      },
      {
        id: 'touch-motor',
        fromOrgan: 'touch',
        toOrgan: 'motor-cortex',
        strength: 0.75,
        type: 'excitatory',
        lastActivation: new Date(),
        activationCount: 0
      }
    ];

    connections.forEach(connection => {
      this.synapticConnections.set(connection.id, connection);
    });

    console.log(`⚡ Established ${connections.length} synaptic connections`);
  }

  /**
   * Démarre le traitement neural en continu
   */
  private startNeuralProcessing(): void {
    setInterval(() => {
      this.processNeuralSignals();
      this.updateOrganMetrics();
      this.adaptSynapticStrengths();
    }, 100); // Traitement toutes les 100ms

    console.log('🧠 Neural processing started');
  }

  /**
   * Lie les événements des agents aux organes
   */
  private bindAgentEvents(): void {
    this.agentManager.on('agent:connected', (agent: AgentConfig) => {
      this.activateOrgansForAgent(agent.id);
    });

    this.agentManager.on('agent:disconnected', (agent: AgentConfig) => {
      this.deactivateOrgansForAgent(agent.id);
    });

    this.agentManager.on('agent:message', (data: any) => {
      this.processAgentMessage(data.agentId, data.message);
    });

    this.agentManager.on('agent:error', (data: any) => {
      this.handleAgentError(data.agentId, data.error);
    });
  }

  /**
   * Active les organes connectés à un agent
   */
  private activateOrgansForAgent(agentId: string): void {
    this.organs.forEach((organ, organId) => {
      if (organ.connectedAgents.includes(agentId)) {
        organ.status = 'active';
        organ.lastActivity = new Date();
        this.organs.set(organId, organ);

        console.log(`✅ Activated organ ${organ.name} for agent ${agentId}`);
        this.emit('organ:activated', { organId, organ, agentId });
      }
    });
  }

  /**
   * Désactive les organes connectés à un agent
   */
  private deactivateOrgansForAgent(agentId: string): void {
    this.organs.forEach((organ, organId) => {
      if (organ.connectedAgents.includes(agentId)) {
        // Vérifier si d'autres agents sont encore connectés
        const otherActiveAgents = organ.connectedAgents.filter(id => {
          const agent = this.agentManager.getAgentStatus(id);
          return agent && agent.status === 'active' && id !== agentId;
        });

        if (otherActiveAgents.length === 0) {
          organ.status = 'inactive';
          this.organs.set(organId, organ);

          console.log(`❌ Deactivated organ ${organ.name} (no active agents)`);
          this.emit('organ:deactivated', { organId, organ, agentId });
        }
      }
    });
  }

  /**
   * Traite un message d'agent et génère des signaux neuraux
   */
  private processAgentMessage(agentId: string, message: AgentMessage): void {
    // Trouver les organes connectés à cet agent
    const connectedOrgans = Array.from(this.organs.entries())
      .filter(([_, organ]) => organ.connectedAgents.includes(agentId))
      .map(([organId, organ]) => ({ organId, organ }));

    connectedOrgans.forEach(({ organId, organ }) => {
      // Créer un signal neural
      const signal: NeuralSignal = {
        id: `signal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        source: organId,
        target: this.findTargetOrgan(organId, message.type),
        type: message.type,
        data: {
          agentId,
          agentMessage: message,
          organId
        },
        timestamp: new Date(),
        priority: this.determinePriority(message.type),
        processed: false
      };

      this.neuralSignals.push(signal);
      this.processingQueue.push(signal);

      // Mettre à jour les métriques de l'organe
      organ.lastActivity = new Date();
      organ.metrics.dataProcessed++;
      organ.metrics.activityLevel = Math.min(100, organ.metrics.activityLevel + 5);
      this.organs.set(organId, organ);

      this.emit('neural:signal-generated', { signal, organ });
    });
  }

  /**
   * Trouve l'organe cible pour un signal basé sur les connexions synaptiques
   */
  private findTargetOrgan(sourceOrganId: string, messageType: string): string {
    // Logique de routage basée sur le type de message et les connexions
    const connections = Array.from(this.synapticConnections.values())
      .filter(conn => conn.fromOrgan === sourceOrganId)
      .sort((a, b) => b.strength - a.strength);

    if (connections.length > 0) {
      // Sélectionner la connexion la plus forte ou une connexion pondérée
      const totalStrength = connections.reduce((sum, conn) => sum + conn.strength, 0);
      const random = Math.random() * totalStrength;

      let cumulative = 0;
      for (const connection of connections) {
        cumulative += connection.strength;
        if (random <= cumulative) {
          return connection.toOrgan;
        }
      }
    }

    // Fallback: retourner le même organe
    return sourceOrganId;
  }

  /**
   * Détermine la priorité d'un signal basé sur le type de message
   */
  private determinePriority(messageType: string): 'low' | 'medium' | 'high' | 'critical' {
    switch (messageType) {
      case 'ERROR':
      case 'SECURITY_ALERT':
        return 'critical';
      case 'WARNING':
      case 'PERFORMANCE_ISSUE':
        return 'high';
      case 'STATUS_UPDATE':
      case 'DATA_UPDATE':
        return 'medium';
      default:
        return 'low';
    }
  }

  /**
   * Traite les signaux neuraux en file d'attente
   */
  private async processNeuralSignals(): Promise<void> {
    if (this.isProcessing || this.processingQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      // Trier par priorité
      this.processingQueue.sort((a, b) => {
        const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      });

      // Traiter jusqu'à 10 signaux par cycle
      const signalsToProcess = this.processingQueue.splice(0, 10);

      for (const signal of signalsToProcess) {
        await this.processIndividualSignal(signal);
      }

    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Traite un signal neural individuel
   */
  private async processIndividualSignal(signal: NeuralSignal): Promise<void> {
    try {
      const sourceOrgan = this.organs.get(signal.source);
      const targetOrgan = this.organs.get(signal.target);

      if (!sourceOrgan || !targetOrgan) {
        console.warn(`⚠️ Invalid signal routing: ${signal.source} -> ${signal.target}`);
        return;
      }

      // Activer la connexion synaptique
      const connectionId = `${signal.source}-${signal.target}`;
      const connection = this.synapticConnections.get(connectionId);

      if (connection) {
        connection.lastActivation = new Date();
        connection.activationCount++;
        this.synapticConnections.set(connectionId, connection);
      }

      // Traitement spécifique selon le type d'organe cible
      await this.processSignalForOrgan(signal, targetOrgan);

      // Marquer le signal comme traité
      signal.processed = true;

      this.emit('neural:signal-processed', { signal, sourceOrgan, targetOrgan });

    } catch (error) {
      console.error(`❌ Error processing neural signal:`, error);
      this.emit('neural:signal-error', { signal, error });
    }
  }

  /**
   * Traite un signal pour un organe spécifique
   */
  private async processSignalForOrgan(signal: NeuralSignal, organ: OrganState): Promise<void> {
    organ.status = 'processing';
    organ.lastActivity = new Date();
    this.organs.set(organ.id, organ);

    const startTime = Date.now();

    try {
      // Logique de traitement selon le type d'organe
      switch (organ.type) {
        case 'sensory':
          await this.processSensorySignal(signal, organ);
          break;
        case 'cognitive':
          await this.processCognitiveSignal(signal, organ);
          break;
        case 'motor':
          await this.processMotorSignal(signal, organ);
          break;
        case 'emotional':
          await this.processEmotionalSignal(signal, organ);
          break;
      }

      // Mettre à jour les métriques
      const responseTime = Date.now() - startTime;
      organ.metrics.responseTime = (organ.metrics.responseTime * 0.9) + (responseTime * 0.1);
      organ.metrics.successRate = Math.min(100, organ.metrics.successRate + 0.1);

    } catch (error) {
      organ.metrics.successRate = Math.max(0, organ.metrics.successRate - 1);
      throw error;
    } finally {
      organ.status = 'active';
      this.organs.set(organ.id, organ);
    }
  }

  /**
   * Traite un signal sensoriel
   */
  private async processSensorySignal(signal: NeuralSignal, organ: OrganState): Promise<void> {
    // Logique spécifique aux organes sensoriels
    switch (organ.id) {
      case 'vision':
        // Traitement des données de recherche web
        if (signal.data.agentMessage.type === 'SEARCH_COMPLETED') {
          this.emit('vision:search-completed', signal.data);
        }
        break;

      case 'hearing':
        // Traitement des flux de données
        if (signal.data.agentMessage.type === 'DATA_STREAM') {
          this.emit('hearing:data-stream', signal.data);
        }
        break;

      case 'touch':
        // Traitement des connexions API
        if (signal.data.agentMessage.type === 'API_STATUS') {
          this.emit('touch:api-status', signal.data);
        }
        break;
    }
  }

  /**
   * Traite un signal cognitif
   */
  private async processCognitiveSignal(signal: NeuralSignal, organ: OrganState): Promise<void> {
    // Logique spécifique aux aires cognitives
    switch (organ.id) {
      case 'broca':
        // Traitement de la communication
        this.emit('broca:communication-signal', signal.data);
        break;

      case 'wernicke':
        // Traitement de la documentation
        this.emit('wernicke:documentation-signal', signal.data);
        break;
    }
  }

  /**
   * Traite un signal moteur
   */
  private async processMotorSignal(signal: NeuralSignal, organ: OrganState): Promise<void> {
    // Logique spécifique au cortex moteur
    if (organ.id === 'motor-cortex') {
      this.emit('motor:action-signal', signal.data);
    }
  }

  /**
   * Traite un signal émotionnel
   */
  private async processEmotionalSignal(signal: NeuralSignal, organ: OrganState): Promise<void> {
    // Logique spécifique aux organes émotionnels
    this.emit('emotional:signal', signal.data);
  }

  /**
   * Met à jour les métriques des organes
   */
  private updateOrganMetrics(): void {
    this.organs.forEach((organ, organId) => {
      // Décroissance naturelle de l'activité
      organ.metrics.activityLevel = Math.max(0, organ.metrics.activityLevel - 0.1);

      // Mise à jour du statut basé sur l'activité
      if (organ.metrics.activityLevel === 0 && organ.status === 'active') {
        const hasActiveAgents = organ.connectedAgents.some(agentId => {
          const agent = this.agentManager.getAgentStatus(agentId);
          return agent && agent.status === 'active';
        });

        if (!hasActiveAgents) {
          organ.status = 'inactive';
        }
      }

      this.organs.set(organId, organ);
    });
  }

  /**
   * Adapte la force des connexions synaptiques (neuroplasticité)
   */
  private adaptSynapticStrengths(): void {
    this.synapticConnections.forEach((connection, connectionId) => {
      const timeSinceLastActivation = Date.now() - connection.lastActivation.getTime();

      // Renforcement si activation récente
      if (timeSinceLastActivation < 60000) { // 1 minute
        connection.strength = Math.min(1.0, connection.strength + 0.001);
      }
      // Affaiblissement si pas d'activation récente
      else if (timeSinceLastActivation > 300000) { // 5 minutes
        connection.strength = Math.max(0.1, connection.strength - 0.0001);
      }

      this.synapticConnections.set(connectionId, connection);
    });
  }

  /**
   * Gère les erreurs d'agent
   */
  private handleAgentError(agentId: string, error: any): void {
    // Créer un signal d'erreur critique
    const errorSignal: NeuralSignal = {
      id: `error_${Date.now()}`,
      source: 'system',
      target: 'all',
      type: 'ERROR',
      data: { agentId, error },
      timestamp: new Date(),
      priority: 'critical',
      processed: false
    };

    this.neuralSignals.push(errorSignal);
    this.processingQueue.unshift(errorSignal); // Priorité maximale

    this.emit('neural:error-signal', { agentId, error, signal: errorSignal });
  }

  /**
   * Obtient l'état de tous les organes
   */
  public getAllOrgansState(): OrganState[] {
    return Array.from(this.organs.values());
  }

  /**
   * Obtient l'état d'un organe spécifique
   */
  public getOrganState(organId: string): OrganState | undefined {
    return this.organs.get(organId);
  }

  /**
   * Obtient toutes les connexions synaptiques
   */
  public getSynapticConnections(): SynapticConnection[] {
    return Array.from(this.synapticConnections.values());
  }

  /**
   * Obtient les signaux neuraux récents
   */
  public getRecentNeuralSignals(limit: number = 50): NeuralSignal[] {
    return this.neuralSignals
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * Envoie un signal manuel à un organe
   */
  public sendSignalToOrgan(organId: string, signalType: string, data: any): void {
    const signal: NeuralSignal = {
      id: `manual_${Date.now()}`,
      source: 'manual',
      target: organId,
      type: signalType,
      data,
      timestamp: new Date(),
      priority: 'medium',
      processed: false
    };

    this.neuralSignals.push(signal);
    this.processingQueue.push(signal);

    this.emit('neural:manual-signal', signal);
  }
}

// Export pour utilisation dans les interfaces
export default HanumanOrganOrchestrator;
