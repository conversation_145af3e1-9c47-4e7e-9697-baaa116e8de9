import React, { useState, useEffect, useRef } from 'react';
import { Eye, Search, Globe, TrendingUp, AlertCircle, CheckCircle, Activity, Wifi, WifiOff, Zap, Database, Clock, BarChart3 } from 'lucide-react';

// Interface pour l'Agent Web Research
interface WebSearchResult {
  id: string;
  query: string;
  results: Array<{
    title: string;
    url: string;
    snippet: string;
    relevance: number;
    timestamp: Date;
  }>;
  quality: number;
  sources: string[];
  metadata: Record<string, any>;
}

interface DataSource {
  id: string;
  name: string;
  url: string;
  status: 'active' | 'inactive' | 'error';
  lastCheck: Date;
  responseTime: number;
  reliability: number;
}

interface WeakSignal {
  id: string;
  description: string;
  confidence: number;
  source: string;
  timestamp: Date;
  category: 'trend' | 'anomaly' | 'opportunity' | 'threat';
}

const HanumanVisionInterface = ({ darkMode = true }) => {
  const [webSearches, setWebSearches] = useState<WebSearchResult[]>([]);
  const [activeQueries, setActiveQueries] = useState<string[]>([]);
  const [dataQuality, setDataQuality] = useState(87.5);
  const [sourcesMonitored, setSourcesMonitored] = useState<DataSource[]>([]);
  const [weakSignals, setWeakSignals] = useState<WeakSignal[]>([]);
  const [visionMetrics, setVisionMetrics] = useState({
    totalSearches: 1247,
    activeConnections: 23,
    averageQuality: 87.5,
    responseTime: 142,
    successRate: 98.7
  });
  const [isConnected, setIsConnected] = useState(false);
  const wsRef = useRef<WebSocket | null>(null);

  // Connexion WebSocket avec l'Agent Web Research
  useEffect(() => {
    const connectToWebResearchAgent = () => {
      try {
        // Connexion à l'agent web-research sur le port configuré
        wsRef.current = new WebSocket('ws://localhost:3001/vision');
        
        wsRef.current.onopen = () => {
          console.log('🔗 Connexion établie avec Agent Web Research');
          setIsConnected(true);
          
          // Demander le statut initial
          wsRef.current?.send(JSON.stringify({
            type: 'GET_STATUS',
            timestamp: Date.now()
          }));
        };

        wsRef.current.onmessage = (event) => {
          const data = JSON.parse(event.data);
          handleWebResearchMessage(data);
        };

        wsRef.current.onclose = () => {
          console.log('❌ Connexion fermée avec Agent Web Research');
          setIsConnected(false);
          
          // Tentative de reconnexion après 5 secondes
          setTimeout(connectToWebResearchAgent, 5000);
        };

        wsRef.current.onerror = (error) => {
          console.error('🚨 Erreur WebSocket:', error);
          setIsConnected(false);
        };

      } catch (error) {
        console.error('🚨 Erreur de connexion:', error);
        setIsConnected(false);
      }
    };

    connectToWebResearchAgent();

    // Simulation de données en attendant la connexion réelle
    const simulationInterval = setInterval(() => {
      if (!isConnected) {
        simulateVisionActivity();
      }
    }, 3000);

    return () => {
      clearInterval(simulationInterval);
      wsRef.current?.close();
    };
  }, []);

  const handleWebResearchMessage = (data: any) => {
    switch (data.type) {
      case 'SEARCH_STARTED':
        setActiveQueries(prev => [...prev, data.query]);
        break;
        
      case 'SEARCH_COMPLETED':
        setWebSearches(prev => [data.result, ...prev.slice(0, 49)]);
        setActiveQueries(prev => prev.filter(q => q !== data.query));
        updateDataQuality(data.result.quality);
        break;
        
      case 'SOURCES_UPDATE':
        setSourcesMonitored(data.sources);
        break;
        
      case 'WEAK_SIGNAL_DETECTED':
        setWeakSignals(prev => [data.signal, ...prev.slice(0, 9)]);
        break;
        
      case 'METRICS_UPDATE':
        setVisionMetrics(data.metrics);
        break;
        
      default:
        console.log('📨 Message non géré:', data);
    }
  };

  const simulateVisionActivity = () => {
    // Simulation d'activité de recherche
    const queries = [
      'AI trends 2024',
      'React best practices',
      'Microservices architecture',
      'Machine learning algorithms',
      'Web development frameworks'
    ];
    
    const randomQuery = queries[Math.floor(Math.random() * queries.length)];
    
    // Simuler une recherche
    setActiveQueries(prev => [...prev, randomQuery]);
    
    setTimeout(() => {
      const mockResult: WebSearchResult = {
        id: `search_${Date.now()}`,
        query: randomQuery,
        results: [
          {
            title: `Results for ${randomQuery}`,
            url: 'https://example.com',
            snippet: 'Mock search result snippet...',
            relevance: Math.random() * 100,
            timestamp: new Date()
          }
        ],
        quality: 70 + Math.random() * 30,
        sources: ['web', 'news'],
        metadata: {}
      };
      
      setWebSearches(prev => [mockResult, ...prev.slice(0, 49)]);
      setActiveQueries(prev => prev.filter(q => q !== randomQuery));
      updateDataQuality(mockResult.quality);
    }, 2000);
  };

  const updateDataQuality = (newQuality: number) => {
    setDataQuality(prev => (prev * 0.9 + newQuality * 0.1));
  };

  const getQualityColor = (quality: number) => {
    if (quality > 80) return 'text-green-400';
    if (quality > 60) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="text-green-400" size={16} />;
      case 'inactive': return <AlertCircle className="text-yellow-400" size={16} />;
      case 'error': return <AlertCircle className="text-red-400" size={16} />;
      default: return <AlertCircle className="text-gray-400" size={16} />;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'trend': return <TrendingUp className="text-blue-400" size={16} />;
      case 'anomaly': return <AlertCircle className="text-orange-400" size={16} />;
      case 'opportunity': return <CheckCircle className="text-green-400" size={16} />;
      case 'threat': return <AlertCircle className="text-red-400" size={16} />;
      default: return <Activity className="text-gray-400" size={16} />;
    }
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      <div className="container mx-auto p-6">
        
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
              <Eye className="text-white" size={24} />
            </div>
            <div>
              <h1 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Vision d'Hanuman
              </h1>
              <p className={`text-lg ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Organe de Recherche Web • Agent Web Research
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${
              isConnected 
                ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' 
                : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
            }`}>
              {isConnected ? <Wifi size={16} /> : <WifiOff size={16} />}
              <span className="text-sm font-medium">
                {isConnected ? 'Connecté' : 'Déconnecté'}
              </span>
            </div>
          </div>
        </div>

        {/* Métriques Globales */}
        <div className={`p-6 rounded-2xl mb-8 ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
          <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Métriques de Vision
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-400">
                {visionMetrics.totalSearches.toLocaleString()}
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Recherches Totales
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-green-400">
                {visionMetrics.activeConnections}
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Connexions Actives
              </div>
            </div>
            
            <div className="text-center">
              <div className={`text-3xl font-bold ${getQualityColor(dataQuality)}`}>
                {dataQuality.toFixed(1)}%
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Qualité Moyenne
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-400">
                {visionMetrics.responseTime}ms
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Temps Réponse
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-400">
                {visionMetrics.successRate}%
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Taux Succès
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          
          {/* Activité de Recherche */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              🔍 Activité de Recherche
            </h3>
            
            {activeQueries.length > 0 && (
              <div className="mb-4">
                <h4 className={`text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Recherches en cours:
                </h4>
                {activeQueries.map((query, index) => (
                  <div key={index} className={`flex items-center space-x-2 p-2 rounded ${darkMode ? 'bg-gray-700' : 'bg-gray-100'} mb-2`}>
                    <Search className="animate-spin text-blue-400" size={16} />
                    <span className={`text-sm ${darkMode ? 'text-white' : 'text-gray-900'}`}>{query}</span>
                  </div>
                ))}
              </div>
            )}
            
            <div className="space-y-3">
              <h4 className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Recherches récentes:
              </h4>
              {webSearches.slice(0, 5).map((search) => (
                <div key={search.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      {search.query}
                    </span>
                    <span className={`text-xs ${getQualityColor(search.quality)}`}>
                      {search.quality.toFixed(0)}%
                    </span>
                  </div>
                  <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {search.results.length} résultats • {search.sources.join(', ')}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Sources Surveillées */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              🌐 Sources Surveillées
            </h3>
            <div className="space-y-3">
              {sourcesMonitored.length === 0 ? (
                // Sources simulées
                [
                  { id: '1', name: 'Google Search', status: 'active', responseTime: 120, reliability: 98 },
                  { id: '2', name: 'Bing Search', status: 'active', responseTime: 150, reliability: 95 },
                  { id: '3', name: 'DuckDuckGo', status: 'active', responseTime: 180, reliability: 92 },
                  { id: '4', name: 'Reddit API', status: 'inactive', responseTime: 0, reliability: 0 },
                  { id: '5', name: 'News API', status: 'active', responseTime: 200, reliability: 89 }
                ].map(source => (
                  <div key={source.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Globe size={16} className="text-blue-400" />
                        <div>
                          <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                            {source.name}
                          </span>
                          <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            {source.responseTime}ms • {source.reliability}% fiabilité
                          </div>
                        </div>
                      </div>
                      {getStatusIcon(source.status)}
                    </div>
                  </div>
                ))
              ) : (
                sourcesMonitored.map(source => (
                  <div key={source.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Globe size={16} className="text-blue-400" />
                        <div>
                          <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                            {source.name}
                          </span>
                          <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            {source.responseTime}ms • {source.reliability}% fiabilité
                          </div>
                        </div>
                      </div>
                      {getStatusIcon(source.status)}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Signaux Faibles Détectés */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              📡 Signaux Faibles
            </h3>
            <div className="space-y-3">
              {weakSignals.length === 0 ? (
                // Signaux simulés
                [
                  { id: '1', description: 'Augmentation recherches IA', confidence: 85, category: 'trend', source: 'Google Trends' },
                  { id: '2', description: 'Anomalie trafic API', confidence: 72, category: 'anomaly', source: 'Monitoring' },
                  { id: '3', description: 'Opportunité marché émergent', confidence: 68, category: 'opportunity', source: 'News Analysis' }
                ].map(signal => (
                  <div key={signal.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                    <div className="flex items-center space-x-3 mb-2">
                      {getCategoryIcon(signal.category)}
                      <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                        {signal.description}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {signal.source}
                      </span>
                      <span className={`text-xs font-medium ${
                        signal.confidence > 80 ? 'text-green-400' :
                        signal.confidence > 60 ? 'text-yellow-400' : 'text-red-400'
                      }`}>
                        {signal.confidence}% confiance
                      </span>
                    </div>
                  </div>
                ))
              ) : (
                weakSignals.map(signal => (
                  <div key={signal.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                    <div className="flex items-center space-x-3 mb-2">
                      {getCategoryIcon(signal.category)}
                      <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                        {signal.description}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {signal.source}
                      </span>
                      <span className={`text-xs font-medium ${
                        signal.confidence > 80 ? 'text-green-400' :
                        signal.confidence > 60 ? 'text-yellow-400' : 'text-red-400'
                      }`}>
                        {signal.confidence}% confiance
                      </span>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HanumanVisionInterface;
