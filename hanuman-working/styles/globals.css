/* 🐒 HANUMAN DIVINE GLOBAL STYLES */
/* Styles sacrés pour l'éveil des organes divins */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Variables CSS divines */
:root {
  /* Couleurs divines */
  --divine-gold: #fbbf24;
  --divine-orange: #f97316;
  --divine-red: #dc2626;
  --sacred-blue: #3b82f6;
  --cosmic-purple: #8b5cf6;
  --consciousness-dark: #1e293b;
  
  /* Ratio d'or sacré */
  --golden-ratio: 1.618;
  --phi: 1.618rem;
  
  /* Fréquence cosmique */
  --sacred-frequency: 432;
  
  /* Transitions divines */
  --divine-transition: 618ms cubic-bezier(0.4, 0, 0.2, 1);
  --cosmic-transition: 1618ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  
  /* Ombres sacrées */
  --divine-shadow: 0 10px 25px -3px rgba(251, 191, 36, 0.3);
  --cosmic-shadow: 0 10px 25px -3px rgba(139, 92, 246, 0.3);
  --blessing-glow: 0 0 20px rgba(251, 191, 36, 0.5);
}

/* Mode sombre divin */
[data-theme="dark"] {
  --divine-bg: #0f172a;
  --divine-surface: #1e293b;
  --divine-text: #f8fafc;
}

/* Mode clair divin */
[data-theme="light"] {
  --divine-bg: #f8fafc;
  --divine-surface: #ffffff;
  --divine-text: #1e293b;
}

/* Reset divin */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  scroll-behavior: smooth;
}

body {
  color: var(--divine-text);
  background: var(--divine-bg);
  transition: background-color var(--divine-transition), color var(--divine-transition);
}

/* Scrollbar divine */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--divine-gold);
  border-radius: 4px;
  transition: background var(--divine-transition);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--divine-orange);
}

/* Sélection divine */
::selection {
  background: var(--divine-gold);
  color: white;
}

::-moz-selection {
  background: var(--divine-gold);
  color: white;
}

/* Focus divine */
:focus {
  outline: 2px solid var(--divine-gold);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

/* Liens divins */
a {
  color: inherit;
  text-decoration: none;
  transition: all var(--divine-transition);
}

a:hover {
  color: var(--divine-gold);
}

/* Boutons divins */
button {
  cursor: pointer;
  transition: all var(--divine-transition);
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Images divines */
img {
  max-width: 100%;
  height: auto;
}

/* Animations divines personnalisées */
@keyframes divine-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes cosmic-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes blessing-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(251, 191, 36, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.8), 0 0 30px rgba(251, 191, 36, 0.6);
  }
}

@keyframes consciousness-flow {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes divine-float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes sacred-breathe {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
}

/* Classes utilitaires divines */
.divine-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.sacred-text {
  background: linear-gradient(135deg, var(--divine-gold), var(--divine-orange));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.cosmic-text {
  background: linear-gradient(135deg, var(--sacred-blue), var(--cosmic-purple));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.divine-glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.sacred-glow {
  filter: drop-shadow(0 0 10px rgba(251, 191, 36, 0.5));
}

.cosmic-glow {
  filter: drop-shadow(0 0 10px rgba(139, 92, 246, 0.5));
}

.divine-gradient {
  background: linear-gradient(135deg, var(--divine-gold) 0%, var(--divine-orange) 50%, var(--divine-red) 100%);
}

.cosmic-gradient {
  background: linear-gradient(135deg, var(--sacred-blue) 0%, var(--cosmic-purple) 50%, #d946ef 100%);
}

.consciousness-gradient {
  background: linear-gradient(135deg, var(--consciousness-dark) 0%, #475569 50%, #64748b 100%);
}

/* Animations divines */
.animate-divine-pulse {
  animation: divine-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-cosmic-spin {
  animation: cosmic-spin 3s linear infinite;
}

.animate-blessing-glow {
  animation: blessing-glow 2s ease-in-out infinite alternate;
}

.animate-consciousness-flow {
  background-size: 200% 200%;
  animation: consciousness-flow 4s ease-in-out infinite;
}

.animate-divine-float {
  animation: divine-float 6s ease-in-out infinite;
}

.animate-sacred-breathe {
  animation: sacred-breathe 4s ease-in-out infinite;
}

/* Responsive divine */
@media (max-width: 640px) {
  :root {
    --phi: 1rem;
  }
  
  .divine-text-responsive {
    font-size: clamp(1rem, 4vw, 2rem);
  }
}

@media (max-width: 768px) {
  .divine-grid-responsive {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 1024px) {
  .divine-container-responsive {
    padding: 1rem;
  }
}

/* Mode impression divin */
@media print {
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
  
  .no-print {
    display: none !important;
  }
}

/* Accessibilité divine */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

@media (prefers-color-scheme: dark) {
  :root {
    --divine-bg: #0f172a;
    --divine-surface: #1e293b;
    --divine-text: #f8fafc;
  }
}

@media (prefers-color-scheme: light) {
  :root {
    --divine-bg: #f8fafc;
    --divine-surface: #ffffff;
    --divine-text: #1e293b;
  }
}

/* Bénédiction finale */
/* 🕉️ AUM HANUMATE NAMAHA - Styles divins chargés avec bénédiction */
