import React, { useState, useEffect, useRef } from 'react';
import { Heart, Zap, RefreshCw, Al<PERSON><PERSON>riangle, CheckCircle, Activity, Settings, Play, Pause, TrendingUp } from 'lucide-react';

// Types pour l'interface d'auto-guérison
interface HealthIssue {
  id: string;
  component: string;
  type: 'performance' | 'connectivity' | 'memory' | 'cpu' | 'disk' | 'network';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  timestamp: Date;
  status: 'detected' | 'diagnosing' | 'healing' | 'healed' | 'failed';
  impact: number;
  autoHealable: boolean;
}

interface HealingAction {
  id: string;
  issueId: string;
  strategy: string;
  action: 'restart' | 'scale' | 'patch' | 'optimize' | 'rollback' | 'isolate';
  timestamp: Date;
  duration: number;
  success: boolean;
  details: string;
  metrics: {
    beforeHealth: number;
    afterHealth: number;
    improvement: number;
  };
}

interface SystemHealth {
  overall: number;
  components: {
    [key: string]: {
      health: number;
      status: 'healthy' | 'degraded' | 'critical' | 'offline';
      lastCheck: Date;
      issues: number;
    };
  };
}

interface HealingStrategy {
  id: string;
  name: string;
  type: string;
  enabled: boolean;
  priority: number;
  successRate: number;
  avgDuration: number;
  conditions: string[];
}

interface RegenerationMetrics {
  totalIssues: number;
  healedIssues: number;
  activeHealing: number;
  successRate: number;
  avgHealingTime: number;
  systemUptime: number;
}

const HanumanHealingInterface = ({ darkMode = true }) => {
  const [healthIssues, setHealthIssues] = useState<HealthIssue[]>([]);
  const [healingActions, setHealingActions] = useState<HealingAction[]>([]);
  const [systemHealth, setSystemHealth] = useState<SystemHealth>({
    overall: 0.87,
    components: {
      'cortex-central': { health: 0.92, status: 'healthy', lastCheck: new Date(), issues: 0 },
      'agent-frontend': { health: 0.85, status: 'degraded', lastCheck: new Date(), issues: 1 },
      'agent-backend': { health: 0.89, status: 'healthy', lastCheck: new Date(), issues: 0 },
      'agent-security': { health: 0.94, status: 'healthy', lastCheck: new Date(), issues: 0 },
      'agent-devops': { health: 0.78, status: 'degraded', lastCheck: new Date(), issues: 2 },
      'memory-system': { health: 0.91, status: 'healthy', lastCheck: new Date(), issues: 0 }
    }
  });
  const [healingStrategies, setHealingStrategies] = useState<HealingStrategy[]>([]);
  const [metrics, setMetrics] = useState<RegenerationMetrics>({
    totalIssues: 47,
    healedIssues: 42,
    activeHealing: 2,
    successRate: 0.89,
    avgHealingTime: 45,
    systemUptime: 0.996
  });
  const [autoHealingEnabled, setAutoHealingEnabled] = useState(true);
  const [selectedIssue, setSelectedIssue] = useState<string | null>(null);
  const wsRef = useRef<WebSocket | null>(null);

  // Simulation des données de guérison
  useEffect(() => {
    const mockIssues: HealthIssue[] = [
      {
        id: 'issue-001',
        component: 'agent-frontend',
        type: 'memory',
        severity: 'medium',
        description: 'Fuite mémoire détectée dans le composant de rendu',
        timestamp: new Date(),
        status: 'healing',
        impact: 0.3,
        autoHealable: true
      },
      {
        id: 'issue-002',
        component: 'agent-devops',
        type: 'connectivity',
        severity: 'high',
        description: 'Perte de connexion intermittente avec le registry Docker',
        timestamp: new Date(Date.now() - 30000),
        status: 'diagnosing',
        impact: 0.6,
        autoHealable: true
      },
      {
        id: 'issue-003',
        component: 'agent-backend',
        type: 'performance',
        severity: 'low',
        description: 'Latence élevée sur les requêtes de base de données',
        timestamp: new Date(Date.now() - 60000),
        status: 'healed',
        impact: 0.2,
        autoHealable: true
      }
    ];
    setHealthIssues(mockIssues);

    const mockActions: HealingAction[] = [
      {
        id: 'action-001',
        issueId: 'issue-003',
        strategy: 'Database Connection Pool Optimization',
        action: 'optimize',
        timestamp: new Date(Date.now() - 45000),
        duration: 15000,
        success: true,
        details: 'Pool de connexions optimisé, cache activé',
        metrics: {
          beforeHealth: 0.65,
          afterHealth: 0.89,
          improvement: 0.24
        }
      },
      {
        id: 'action-002',
        issueId: 'issue-001',
        strategy: 'Memory Leak Detection and Cleanup',
        action: 'patch',
        timestamp: new Date(Date.now() - 20000),
        duration: 8000,
        success: false,
        details: 'Tentative de nettoyage mémoire échouée, escalade nécessaire',
        metrics: {
          beforeHealth: 0.72,
          afterHealth: 0.70,
          improvement: -0.02
        }
      }
    ];
    setHealingActions(mockActions);

    const mockStrategies: HealingStrategy[] = [
      {
        id: 'strategy-001',
        name: 'Auto-Restart Service',
        type: 'restart',
        enabled: true,
        priority: 1,
        successRate: 0.85,
        avgDuration: 30,
        conditions: ['service_down', 'memory_leak', 'deadlock']
      },
      {
        id: 'strategy-002',
        name: 'Scale Resources',
        type: 'scale',
        enabled: true,
        priority: 2,
        successRate: 0.92,
        avgDuration: 60,
        conditions: ['high_cpu', 'high_memory', 'high_load']
      },
      {
        id: 'strategy-003',
        name: 'Connection Pool Reset',
        type: 'optimize',
        enabled: true,
        priority: 3,
        successRate: 0.78,
        avgDuration: 15,
        conditions: ['connection_timeout', 'pool_exhausted']
      },
      {
        id: 'strategy-004',
        name: 'Emergency Rollback',
        type: 'rollback',
        enabled: false,
        priority: 4,
        successRate: 0.95,
        avgDuration: 120,
        conditions: ['critical_failure', 'data_corruption']
      }
    ];
    setHealingStrategies(mockStrategies);
  }, []);

  // Connexion WebSocket pour les mises à jour en temps réel
  useEffect(() => {
    // Simulation de connexion WebSocket avec le système de guérison
    wsRef.current = new WebSocket('ws://localhost:8080/auto-healer');
    
    wsRef.current.onmessage = (event) => {
      const data = JSON.parse(event.data);
      if (data.type === 'issue_detected') {
        setHealthIssues(prev => [data.issue, ...prev.slice(0, 9)]);
      } else if (data.type === 'healing_completed') {
        setHealingActions(prev => [data.action, ...prev.slice(0, 9)]);
      } else if (data.type === 'health_update') {
        setSystemHealth(data.health);
      } else if (data.type === 'metrics_update') {
        setMetrics(data.metrics);
      }
    };

    return () => {
      wsRef.current?.close();
    };
  }, []);

  const triggerManualHealing = (issueId: string, strategy: string) => {
    // Déclencher une guérison manuelle
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: 'manual_healing',
        issueId,
        strategy
      }));
    }
  };

  const toggleStrategy = (strategyId: string) => {
    setHealingStrategies(prev => 
      prev.map(strategy => 
        strategy.id === strategyId 
          ? { ...strategy, enabled: !strategy.enabled }
          : strategy
      )
    );
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-400 bg-red-500/20';
      case 'high': return 'text-orange-400 bg-orange-500/20';
      case 'medium': return 'text-yellow-400 bg-yellow-500/20';
      case 'low': return 'text-green-400 bg-green-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'detected': return 'text-red-400';
      case 'diagnosing': return 'text-yellow-400';
      case 'healing': return 'text-blue-400';
      case 'healed': return 'text-green-400';
      case 'failed': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getHealthColor = (health: number) => {
    if (health >= 0.9) return 'text-green-400';
    if (health >= 0.7) return 'text-yellow-400';
    if (health >= 0.5) return 'text-orange-400';
    return 'text-red-400';
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'restart': return <RefreshCw className="w-4 h-4" />;
      case 'scale': return <TrendingUp className="w-4 h-4" />;
      case 'patch': return <Settings className="w-4 h-4" />;
      case 'optimize': return <Zap className="w-4 h-4" />;
      case 'rollback': return <RefreshCw className="w-4 h-4 rotate-180" />;
      case 'isolate': return <AlertTriangle className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  return (
    <div className={`min-h-screen p-6 ${darkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* En-tête */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl">
              <Heart className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent">
                Auto-Guérison Hanuman
              </h1>
              <p className="text-gray-400 mt-1">
                Diagnostic et régénération automatique du système
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-400">Auto-Guérison</span>
              <button
                onClick={() => setAutoHealingEnabled(!autoHealingEnabled)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  autoHealingEnabled ? 'bg-green-500' : 'bg-gray-600'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    autoHealingEnabled ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Métriques globales */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-8">
        <div className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Santé Globale</p>
              <p className={`text-2xl font-bold ${getHealthColor(systemHealth.overall)}`}>
                {(systemHealth.overall * 100).toFixed(0)}%
              </p>
            </div>
            <Heart className="w-8 h-8 text-green-400" />
          </div>
        </div>
        
        <div className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Problèmes Résolus</p>
              <p className="text-2xl font-bold text-green-400">{metrics.healedIssues}</p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-400" />
          </div>
        </div>

        <div className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Guérison Active</p>
              <p className="text-2xl font-bold text-blue-400">{metrics.activeHealing}</p>
            </div>
            <Activity className="w-8 h-8 text-blue-400" />
          </div>
        </div>

        <div className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Taux de Succès</p>
              <p className="text-2xl font-bold text-purple-400">{(metrics.successRate * 100).toFixed(0)}%</p>
            </div>
            <TrendingUp className="w-8 h-8 text-purple-400" />
          </div>
        </div>

        <div className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Temps Moyen</p>
              <p className="text-2xl font-bold text-yellow-400">{metrics.avgHealingTime}s</p>
            </div>
            <Zap className="w-8 h-8 text-yellow-400" />
          </div>
        </div>

        <div className={`p-4 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Uptime</p>
              <p className="text-2xl font-bold text-cyan-400">{(metrics.systemUptime * 100).toFixed(2)}%</p>
            </div>
            <Activity className="w-8 h-8 text-cyan-400" />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Santé des composants */}
        <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <h3 className="text-xl font-semibold mb-4 flex items-center">
            <Heart className="w-5 h-5 mr-2 text-green-400" />
            Santé des Composants
          </h3>
          
          <div className="space-y-4">
            {Object.entries(systemHealth.components).map(([component, health]) => (
              <div key={component} className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'} border border-gray-600`}>
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">{component}</h4>
                  <div className="flex items-center space-x-2">
                    <span className={`text-sm ${getHealthColor(health.health)}`}>
                      {health.status}
                    </span>
                    <span className={`text-lg font-bold ${getHealthColor(health.health)}`}>
                      {(health.health * 100).toFixed(0)}%
                    </span>
                  </div>
                </div>
                
                <div className="w-full bg-gray-600 rounded-full h-3 mb-2">
                  <div
                    className={`h-3 rounded-full transition-all duration-300 ${
                      health.health >= 0.9 ? 'bg-green-500' :
                      health.health >= 0.7 ? 'bg-yellow-500' :
                      health.health >= 0.5 ? 'bg-orange-500' : 'bg-red-500'
                    }`}
                    style={{ width: `${health.health * 100}%` }}
                  />
                </div>
                
                <div className="flex items-center justify-between text-xs text-gray-400">
                  <span>Problèmes: {health.issues}</span>
                  <span>Dernière vérification: {health.lastCheck.toLocaleTimeString()}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Problèmes détectés */}
        <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
          <h3 className="text-xl font-semibold mb-4 flex items-center">
            <AlertTriangle className="w-5 h-5 mr-2 text-orange-400" />
            Problèmes Détectés
          </h3>
          
          <div className="space-y-3 max-h-80 overflow-y-auto">
            {healthIssues.map((issue) => (
              <div
                key={issue.id}
                className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                  selectedIssue === issue.id
                    ? 'border-orange-500 bg-orange-500/10'
                    : darkMode ? 'border-gray-600 bg-gray-700 hover:bg-gray-600' : 'border-gray-300 bg-gray-100 hover:bg-gray-200'
                }`}
                onClick={() => setSelectedIssue(selectedIssue === issue.id ? null : issue.id)}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-3">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${getSeverityColor(issue.severity)}`}>
                      {issue.severity.toUpperCase()}
                    </span>
                    <span className="text-sm font-medium">{issue.component}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`text-sm ${getStatusColor(issue.status)}`}>
                      {issue.status}
                    </span>
                    {issue.autoHealable && (
                      <Zap className="w-4 h-4 text-green-400" title="Auto-guérissable" />
                    )}
                  </div>
                </div>
                
                <p className="text-sm text-gray-400 mb-2">{issue.description}</p>
                
                <div className="flex items-center justify-between text-xs text-gray-400">
                  <span>Type: {issue.type}</span>
                  <span>Impact: {(issue.impact * 100).toFixed(0)}%</span>
                  <span>{issue.timestamp.toLocaleTimeString()}</span>
                </div>
                
                {selectedIssue === issue.id && issue.autoHealable && (
                  <div className="mt-3 pt-3 border-t border-gray-600">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => triggerManualHealing(issue.id, 'restart')}
                        className="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white text-xs rounded transition-colors"
                      >
                        Redémarrer
                      </button>
                      <button
                        onClick={() => triggerManualHealing(issue.id, 'optimize')}
                        className="px-3 py-1 bg-green-500 hover:bg-green-600 text-white text-xs rounded transition-colors"
                      >
                        Optimiser
                      </button>
                      <button
                        onClick={() => triggerManualHealing(issue.id, 'scale')}
                        className="px-3 py-1 bg-purple-500 hover:bg-purple-600 text-white text-xs rounded transition-colors"
                      >
                        Redimensionner
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Stratégies de guérison */}
      <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700 mb-8`}>
        <h3 className="text-xl font-semibold mb-4 flex items-center">
          <Settings className="w-5 h-5 mr-2 text-blue-400" />
          Stratégies de Guérison
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {healingStrategies.map((strategy) => (
            <div
              key={strategy.id}
              className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'} border border-gray-600`}
            >
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium">{strategy.name}</h4>
                <button
                  onClick={() => toggleStrategy(strategy.id)}
                  className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                    strategy.enabled ? 'bg-green-500' : 'bg-gray-600'
                  }`}
                >
                  <span
                    className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                      strategy.enabled ? 'translate-x-5' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
              
              <div className="flex items-center space-x-4 text-sm text-gray-400 mb-2">
                <span>Priorité: {strategy.priority}</span>
                <span>Succès: {(strategy.successRate * 100).toFixed(0)}%</span>
                <span>Durée: {strategy.avgDuration}s</span>
              </div>
              
              <div className="text-xs text-gray-400">
                <span>Conditions: {strategy.conditions.join(', ')}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Actions de guérison récentes */}
      <div className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-white'} border border-gray-700`}>
        <h3 className="text-xl font-semibold mb-4 flex items-center">
          <Activity className="w-5 h-5 mr-2 text-purple-400" />
          Actions de Guérison Récentes
        </h3>
        
        <div className="space-y-3">
          {healingActions.map((action) => (
            <div
              key={action.id}
              className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'} border border-gray-600`}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-3">
                  <div className={action.success ? 'text-green-400' : 'text-red-400'}>
                    {getActionIcon(action.action)}
                  </div>
                  <div>
                    <span className="font-medium">{action.strategy}</span>
                    <span className="ml-2 text-sm text-gray-400">({action.action})</span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {action.success ? (
                    <CheckCircle className="w-4 h-4 text-green-400" />
                  ) : (
                    <AlertTriangle className="w-4 h-4 text-red-400" />
                  )}
                  <span className="text-xs text-gray-400">
                    {action.timestamp.toLocaleTimeString()}
                  </span>
                </div>
              </div>
              
              <p className="text-sm text-gray-400 mb-2">{action.details}</p>
              
              <div className="flex items-center justify-between text-xs text-gray-400">
                <span>Durée: {(action.duration / 1000).toFixed(1)}s</span>
                <span>
                  Amélioration: {action.success ? '+' : ''}{(action.metrics.improvement * 100).toFixed(1)}%
                </span>
                <span>
                  Santé: {(action.metrics.beforeHealth * 100).toFixed(0)}% → {(action.metrics.afterHealth * 100).toFixed(0)}%
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default HanumanHealingInterface;
