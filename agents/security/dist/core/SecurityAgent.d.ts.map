{"version": 3, "file": "SecurityAgent.d.ts", "sourceRoot": "", "sources": ["../../src/core/SecurityAgent.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,EAAE,MAAM,EAAE,MAAM,SAAS,CAAC;AACjC,OAAO,EACL,mBAAmB,EACnB,mBAAmB,EAMnB,eAAe,EAChB,MAAM,UAAU,CAAC;AAClB,OAAO,EAAE,cAAc,EAAE,MAAM,0BAA0B,CAAC;AAC1D,OAAO,EAAE,kBAAkB,EAAE,MAAM,qCAAqC,CAAC;AAYzE;;;;;GAKG;AACH,qBAAa,aAAc,SAAQ,YAAY;IAC7C,OAAO,CAAC,MAAM,CAAsB;IACpC,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,MAAM,CAAiB;IAC/B,OAAO,CAAC,aAAa,CAAqB;IAE1C,OAAO,CAAC,oBAAoB,CAAwB;IACpD,OAAO,CAAC,iBAAiB,CAAqB;IAC9C,OAAO,CAAC,kBAAkB,CAAsB;IAChD,OAAO,CAAC,gBAAgB,CAAoB;IAC5C,OAAO,CAAC,eAAe,CAAmB;IAC1C,OAAO,CAAC,eAAe,CAAmB;IAC1C,OAAO,CAAC,oBAAoB,CAAwB;IACpD,OAAO,CAAC,WAAW,CAAe;IAClC,OAAO,CAAC,iBAAiB,CAAqB;IAC9C,OAAO,CAAC,oBAAoB,CAAwB;IAEpD,OAAO,CAAC,eAAe,CAA+C;IACtE,OAAO,CAAC,WAAW,CAA8C;IACjE,OAAO,CAAC,eAAe,CAA4C;IACnE,OAAO,CAAC,gBAAgB,CAA2C;IAEnE,OAAO,CAAC,aAAa,CAAkB;IACvC,OAAO,CAAC,iBAAiB,CAAkB;IAC3C,OAAO,CAAC,kBAAkB,CAAa;gBAGrC,MAAM,EAAE,mBAAmB,EAC3B,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,cAAc,EACtB,aAAa,EAAE,kBAAkB;IAUnC;;OAEG;IACG,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAkCjC;;OAEG;YACW,4BAA4B;IAqF1C;;OAEG;YACW,kBAAkB;IA6BhC;;OAEG;YACW,uBAAuB;IAsBrC;;OAEG;YACW,gBAAgB;IAqB9B;;OAEG;IACH,OAAO,CAAC,mBAAmB;IAQ3B;;OAEG;YACW,oBAAoB;IA4BlC;;OAEG;IACG,iBAAiB,CAAC,OAAO,EAAE,mBAAmB,GAAG,OAAO,CAAC,IAAI,CAAC;IAwBpE;;OAEG;YACW,WAAW;IAoGzB;;OAEG;YACW,4BAA4B;IAiB1C;;OAEG;YACW,8BAA8B;IAkC5C;;OAEG;YACW,mBAAmB;IAwBjC;;OAEG;YACW,uBAAuB;IA2BrC;;OAEG;YACW,sBAAsB;IAgBpC;;OAEG;YACW,mBAAmB;IA8BjC,OAAO,CAAC,mBAAmB;IAU3B,OAAO,CAAC,iBAAiB;IAKzB,OAAO,CAAC,oBAAoB;IAU5B,OAAO,CAAC,6BAA6B;IAarC;;OAEG;IACH,SAAS,IAAI,GAAG;IAahB;;OAEG;IACG,kBAAkB,IAAI,OAAO,CAAC,eAAe,CAAC;IAIpD;;OAEG;IACG,WAAW,CACf,MAAM,EAAE,MAAM,EACd,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,MAAM,EACd,OAAO,CAAC,EAAE,GAAG,GACZ,OAAO,CAAC,GAAG,CAAC;IAIf;;OAEG;IACG,iBAAiB,CACrB,MAAM,EAAE,MAAM,EACd,cAAc,EAAE,GAAG,EACnB,UAAU,EAAE,MAAM,GACjB,OAAO,CAAC,GAAG,CAAC;IAIf;;OAEG;IACG,iBAAiB,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAIvE;;OAEG;IACG,aAAa,CAAC,KAAK,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IAI9C;;OAEG;IACG,mBAAmB,CACvB,UAAU,EAAE,MAAM,EAClB,MAAM,EAAE;QAAE,KAAK,EAAE,IAAI,CAAC;QAAC,GAAG,EAAE,IAAI,CAAA;KAAE,EAClC,OAAO,CAAC,EAAE,GAAG,GACZ,OAAO,CAAC,GAAG,CAAC;IAIf;;OAEG;IACG,WAAW,CACf,IAAI,EAAE,MAAM,GAAG,MAAM,EACrB,KAAK,CAAC,EAAE,MAAM,EACd,SAAS,CAAC,EAAE,MAAM,GACjB,OAAO,CAAC,GAAG,CAAC;IAIf;;OAEG;IACG,WAAW,CACf,aAAa,EAAE,MAAM,EACrB,KAAK,EAAE,MAAM,EACb,SAAS,EAAE,MAAM,EACjB,EAAE,CAAC,EAAE,MAAM,GACV,OAAO,CAAC,MAAM,CAAC;IAIlB;;OAEG;IACG,qBAAqB,CACzB,KAAK,EAAE,MAAM,EACb,SAAS,CAAC,EAAE,MAAM,EAClB,OAAO,CAAC,EAAE,MAAM,GACf,OAAO,CAAC,GAAG,CAAC;IAIf;;OAEG;IACG,mBAAmB,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAItD;;OAEG;IACG,oBAAoB,CAAC,UAAU,EAAE,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;IAIzD;;OAEG;IACG,cAAc,CAAC,MAAM,EAAE,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;IAI/C;;OAEG;IACG,oBAAoB,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;IAIxE;;OAEG;IACG,oBAAoB,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAI3D;;OAEG;IACH,OAAO,CAAC,qBAAqB;IAK7B;;OAEG;IACH,OAAO,CAAC,4BAA4B;IAKpC;;OAEG;IACH,OAAO,CAAC,yBAAyB;IAKjC;;OAEG;IACG,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;CAoBhC"}