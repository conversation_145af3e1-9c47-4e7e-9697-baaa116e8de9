"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityMonitor = void 0;
const events_1 = require("events");
/**
 * Moniteur de Sécurité
 *
 * Surveille les événements de sécurité en temps réel et génère des alertes
 */
class SecurityMonitor extends events_1.EventEmitter {
    constructor(config, logger, memory) {
        super();
        this.isRunning = false;
        this.monitoringIntervals = [];
        this.alertQueue = [];
        this.metrics = this.initializeMetrics();
        this.config = config;
        this.logger = logger;
        this.memory = memory;
    }
    /**
     * Initialise le monitoring de sécurité
     */
    async initialize() {
        this.logger.info('📊 Initialisation du Monitoring de Sécurité...');
        // Initialisation déjà effectuée dans start()
        this.logger.info('✅ Monitoring de Sécurité initialisé');
    }
    /**
     * Démarre le monitoring de sécurité
     */
    async start() {
        try {
            this.logger.info('📊 Démarrage du Monitoring de Sécurité...');
            // Démarrage du monitoring en temps réel
            if (this.config.realTime) {
                await this.startRealTimeMonitoring();
            }
            // Démarrage de la collecte de métriques
            if (this.config.metrics.collection) {
                this.startMetricsCollection();
            }
            // Démarrage du processeur d'alertes
            this.startAlertProcessor();
            // Démarrage des vérifications périodiques
            this.startPeriodicChecks();
            this.isRunning = true;
            this.logger.info('✅ Monitoring de Sécurité démarré');
        }
        catch (error) {
            this.logger.error('❌ Erreur lors du démarrage du monitoring:', error);
            throw error;
        }
    }
    /**
     * Démarre le monitoring en temps réel
     */
    async startRealTimeMonitoring() {
        this.logger.info('🔄 Démarrage du monitoring en temps réel...');
        // Monitoring des vulnérabilités
        const vulnMonitoring = setInterval(async () => {
            await this.checkForNewVulnerabilities();
        }, 30000); // Toutes les 30 secondes
        // Monitoring des menaces
        const threatMonitoring = setInterval(async () => {
            await this.checkForNewThreats();
        }, 60000); // Toutes les minutes
        // Monitoring des incidents
        const incidentMonitoring = setInterval(async () => {
            await this.checkForActiveIncidents();
        }, 45000); // Toutes les 45 secondes
        // Monitoring des métriques système
        const systemMonitoring = setInterval(async () => {
            await this.collectSystemMetrics();
        }, 120000); // Toutes les 2 minutes
        this.monitoringIntervals.push(vulnMonitoring, threatMonitoring, incidentMonitoring, systemMonitoring);
    }
    /**
     * Vérifie les nouvelles vulnérabilités
     */
    async checkForNewVulnerabilities() {
        try {
            const recentScans = await this.memory.getRecentScanResults(10);
            for (const scan of recentScans) {
                const criticalVulns = scan.vulnerabilities.filter(v => v.severity === 'critical');
                const highVulns = scan.vulnerabilities.filter(v => v.severity === 'high');
                if (criticalVulns.length > 0) {
                    await this.generateAlert({
                        id: `vuln-critical-${Date.now()}`,
                        type: 'vulnerability-detection',
                        title: `${criticalVulns.length} vulnérabilités critiques détectées`,
                        description: `Scan ${scan.scanId} a révélé ${criticalVulns.length} vulnérabilités critiques`,
                        severity: 'critical',
                        source: 'SecurityMonitor',
                        timestamp: new Date(),
                        data: {
                            scanId: scan.scanId,
                            vulnerabilities: criticalVulns.length,
                            critical: criticalVulns.length,
                            high: highVulns.length
                        }
                    });
                    this.emit('vulnerability-found', {
                        scanId: scan.scanId,
                        vulnerabilities: criticalVulns,
                        severity: 'critical'
                    });
                }
            }
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de la vérification des vulnérabilités:', error);
        }
    }
    /**
     * Vérifie les nouvelles menaces
     */
    async checkForNewThreats() {
        try {
            const indicators = await this.memory.getThreatIndicators(50);
            const recentIndicators = indicators.filter(i => Date.now() - i.lastSeen.getTime() < 300000 // 5 minutes
            );
            if (recentIndicators.length > 0) {
                const highThreatIndicators = recentIndicators.filter(i => i.threatLevel === 'high' || i.threatLevel === 'critical');
                if (highThreatIndicators.length > 0) {
                    await this.generateAlert({
                        id: `threat-${Date.now()}`,
                        type: 'threat-detection',
                        title: `${highThreatIndicators.length} nouvelles menaces détectées`,
                        description: `${highThreatIndicators.length} indicateurs de menace de haute sévérité détectés`,
                        severity: 'high',
                        source: 'ThreatIntelligence',
                        timestamp: new Date(),
                        data: {
                            indicators: highThreatIndicators.length,
                            types: [...new Set(highThreatIndicators.map(i => i.type))]
                        }
                    });
                    this.emit('threat-detected', {
                        indicators: highThreatIndicators,
                        count: highThreatIndicators.length
                    });
                }
            }
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de la vérification des menaces:', error);
        }
    }
    /**
     * Vérifie les incidents actifs
     */
    async checkForActiveIncidents() {
        try {
            const activeIncidents = await this.memory.getActiveIncidents();
            // Vérification des incidents critiques non résolus
            const criticalIncidents = activeIncidents.filter(i => i.severity === 'critical' && i.status !== 'resolved');
            if (criticalIncidents.length > 0) {
                for (const incident of criticalIncidents) {
                    const timeSinceDetection = Date.now() - incident.detectedAt.getTime();
                    // Alerte si incident critique non résolu depuis plus de 30 minutes
                    if (timeSinceDetection > 1800000) { // 30 minutes
                        await this.generateAlert({
                            id: `incident-escalation-${incident.id}`,
                            type: 'incident-escalation',
                            title: `Incident critique non résolu: ${incident.title}`,
                            description: `L'incident ${incident.id} est critique et non résolu depuis ${Math.round(timeSinceDetection / 60000)} minutes`,
                            severity: 'critical',
                            source: 'IncidentResponse',
                            timestamp: new Date(),
                            data: {
                                incidentId: incident.id,
                                timeSinceDetection: timeSinceDetection,
                                status: incident.status
                            }
                        });
                    }
                }
            }
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de la vérification des incidents:', error);
        }
    }
    /**
     * Collecte les métriques système
     */
    async collectSystemMetrics() {
        try {
            const systemMetrics = {
                timestamp: new Date(),
                memory: {
                    used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
                    total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
                    external: Math.round(process.memoryUsage().external / 1024 / 1024)
                },
                uptime: process.uptime(),
                cpu: process.cpuUsage(),
                activeConnections: this.getActiveConnectionsCount(),
                queueSize: this.alertQueue.length
            };
            // Mise à jour des métriques
            this.updateMetrics(systemMetrics);
            // Vérification des seuils d'alerte
            await this.checkSystemThresholds(systemMetrics);
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de la collecte des métriques:', error);
        }
    }
    /**
     * Génère une alerte de sécurité
     */
    async generateAlert(alert) {
        try {
            // Ajout à la queue d'alertes
            this.alertQueue.push(alert);
            // Stockage en mémoire
            await this.memory.storeSecurityAlert(alert);
            // Émission d'événement
            this.emit('security-alert', alert);
            this.logger.warn(`🚨 Alerte générée: ${alert.title} (${alert.severity})`);
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de la génération d\'alerte:', error);
        }
    }
    /**
     * Démarre le processeur d'alertes
     */
    startAlertProcessor() {
        const processor = setInterval(async () => {
            if (this.alertQueue.length > 0) {
                const alert = this.alertQueue.shift();
                if (alert) {
                    await this.processAlert(alert);
                }
            }
        }, 5000); // Toutes les 5 secondes
        this.monitoringIntervals.push(processor);
    }
    /**
     * Traite une alerte
     */
    async processAlert(alert) {
        try {
            // Vérification des règles d'alerte
            const applicableRules = this.config.alerting.rules.filter(rule => this.evaluateAlertRule(rule, alert));
            for (const rule of applicableRules) {
                // Envoi via les canaux configurés
                for (const channelId of rule.channels) {
                    const channel = this.config.alerting.channels.find(c => c.type === channelId);
                    if (channel && channel.enabled) {
                        await this.sendAlertToChannel(alert, channel);
                    }
                }
            }
        }
        catch (error) {
            this.logger.error('❌ Erreur lors du traitement d\'alerte:', error);
        }
    }
    /**
     * Évalue une règle d'alerte
     */
    evaluateAlertRule(rule, alert) {
        // Logique d'évaluation simplifiée
        if (rule.severity && alert.severity !== rule.severity) {
            return false;
        }
        // Évaluation de la condition (simplifiée)
        return true;
    }
    /**
     * Envoie une alerte via un canal
     */
    async sendAlertToChannel(alert, channel) {
        try {
            switch (channel.type) {
                case 'webhook':
                    // Simulation d'envoi webhook
                    this.logger.info(`📤 Alerte envoyée via webhook: ${alert.title}`);
                    break;
                case 'email':
                    // Simulation d'envoi email
                    this.logger.info(`📧 Alerte envoyée par email: ${alert.title}`);
                    break;
                default:
                    this.logger.warn(`⚠️ Type de canal non supporté: ${channel.type}`);
            }
        }
        catch (error) {
            this.logger.error(`❌ Erreur lors de l'envoi via ${channel.type}:`, error);
        }
    }
    /**
     * Démarre les vérifications périodiques
     */
    startPeriodicChecks() {
        // Vérification de la santé du système toutes les 5 minutes
        const healthCheck = setInterval(async () => {
            await this.performHealthCheck();
        }, 300000);
        this.monitoringIntervals.push(healthCheck);
    }
    /**
     * Effectue une vérification de santé
     */
    async performHealthCheck() {
        try {
            const health = {
                timestamp: new Date(),
                status: 'healthy',
                components: {
                    memory: this.memory.getConnectionStatus(),
                    monitoring: this.isRunning,
                    alertQueue: this.alertQueue.length < 100,
                    uptime: process.uptime() > 0
                }
            };
            const unhealthyComponents = Object.entries(health.components)
                .filter(([_, status]) => !status)
                .map(([name, _]) => name);
            if (unhealthyComponents.length > 0) {
                health.status = 'unhealthy';
                await this.generateAlert({
                    id: `health-check-${Date.now()}`,
                    type: 'system-health',
                    title: 'Problème de santé système détecté',
                    description: `Composants défaillants: ${unhealthyComponents.join(', ')}`,
                    severity: 'medium',
                    source: 'SecurityMonitor',
                    timestamp: new Date(),
                    data: { unhealthyComponents }
                });
            }
            this.logger.debug(`💓 Vérification de santé: ${health.status}`);
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de la vérification de santé:', error);
        }
    }
    /**
     * Démarre la collecte de métriques
     */
    startMetricsCollection() {
        const metricsCollector = setInterval(async () => {
            await this.collectAndExportMetrics();
        }, 60000); // Toutes les minutes
        this.monitoringIntervals.push(metricsCollector);
    }
    /**
     * Collecte et exporte les métriques
     */
    async collectAndExportMetrics() {
        try {
            const securityMetrics = await this.memory.getSecurityMetrics();
            // Mise à jour des métriques locales
            this.metrics = {
                ...this.metrics,
                ...securityMetrics,
                lastUpdate: new Date()
            };
            // Export vers les systèmes configurés
            for (const exportConfig of this.config.metrics.export) {
                if (exportConfig.enabled) {
                    await this.exportMetrics(exportConfig, this.metrics);
                }
            }
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de la collecte de métriques:', error);
        }
    }
    /**
     * Exporte les métriques
     */
    async exportMetrics(exportConfig, metrics) {
        try {
            switch (exportConfig.type) {
                case 'prometheus':
                    // Simulation d'export Prometheus
                    this.logger.debug('📊 Métriques exportées vers Prometheus');
                    break;
                default:
                    this.logger.warn(`⚠️ Type d'export non supporté: ${exportConfig.type}`);
            }
        }
        catch (error) {
            this.logger.error(`❌ Erreur lors de l'export ${exportConfig.type}:`, error);
        }
    }
    // Méthodes utilitaires
    initializeMetrics() {
        return {
            vulnerabilities: {
                total: 0,
                bySeverity: {},
                byCategory: {},
                meanTimeToDetection: 0,
                meanTimeToRemediation: 0,
                trends: []
            },
            compliance: {
                overallScore: 0,
                byFramework: {},
                controlsTotal: 0,
                controlsCompliant: 0,
                controlsNonCompliant: 0,
                trends: []
            },
            incidents: {
                total: 0,
                bySeverity: {},
                byCategory: {},
                meanTimeToDetection: 0,
                meanTimeToContainment: 0,
                meanTimeToResolution: 0,
                trends: []
            },
            threats: {
                indicatorsTotal: 0,
                indicatorsByType: {},
                threatsBlocked: 0,
                threatsDetected: 0,
                falsePositives: 0,
                trends: []
            },
            coverage: {
                assetsScanned: 0,
                assetsTotal: 0,
                coveragePercentage: 0,
                scanFrequency: 0,
                lastScanAge: 0
            }
        };
    }
    updateMetrics(systemMetrics) {
        // Mise à jour des métriques avec les données système
        this.metrics.lastUpdate = new Date();
    }
    async checkSystemThresholds(systemMetrics) {
        // Vérification des seuils système et génération d'alertes si nécessaire
        if (systemMetrics.memory.used > 1000) { // Plus de 1GB
            await this.generateAlert({
                id: `memory-threshold-${Date.now()}`,
                type: 'system-resource',
                title: 'Utilisation mémoire élevée',
                description: `Utilisation mémoire: ${systemMetrics.memory.used}MB`,
                severity: 'medium',
                source: 'SystemMonitor',
                timestamp: new Date(),
                data: systemMetrics.memory
            });
        }
    }
    getActiveConnectionsCount() {
        // Simulation du nombre de connexions actives
        return Math.floor(Math.random() * 100);
    }
    /**
     * Obtient les métriques actuelles
     */
    getMetrics() {
        return this.metrics;
    }
    /**
     * Obtient le statut du monitoring
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            alertQueueSize: this.alertQueue.length,
            activeIntervals: this.monitoringIntervals.length,
            uptime: process.uptime(),
            lastMetricsUpdate: this.metrics.lastUpdate
        };
    }
    /**
     * Envoie une alerte
     */
    async sendAlert(alert) {
        await this.generateAlert({
            id: alert.id || `alert-${Date.now()}`,
            type: alert.type,
            title: alert.title,
            description: alert.description,
            severity: alert.severity,
            source: alert.source || 'SecurityMonitor',
            timestamp: new Date(),
            data: alert.data
        });
    }
    /**
     * Arrête le monitoring
     */
    async stop() {
        this.logger.info('🛑 Arrêt du Monitoring de Sécurité...');
        // Arrêt de tous les intervalles
        this.monitoringIntervals.forEach(interval => clearInterval(interval));
        this.monitoringIntervals = [];
        // Traitement des alertes restantes
        while (this.alertQueue.length > 0) {
            const alert = this.alertQueue.shift();
            if (alert) {
                await this.processAlert(alert);
            }
        }
        this.isRunning = false;
        this.logger.info('✅ Monitoring de Sécurité arrêté');
    }
    /**
     * Arrêt du monitoring (alias pour stop)
     */
    async shutdown() {
        await this.stop();
    }
}
exports.SecurityMonitor = SecurityMonitor;
//# sourceMappingURL=SecurityMonitor.js.map