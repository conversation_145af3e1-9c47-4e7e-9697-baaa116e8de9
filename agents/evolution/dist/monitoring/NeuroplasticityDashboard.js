"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NeuroplasticityDashboard = void 0;
const events_1 = require("events");
/**
 * Dashboard de Monitoring Synaptique
 *
 * Fournit une interface de monitoring en temps réel pour la neuroplasticité,
 * incluant des métriques, visualisations et alertes.
 */
class NeuroplasticityDashboard extends events_1.EventEmitter {
    engine;
    logger;
    isMonitoring = false;
    monitoringInterval = null;
    metricsHistory = [];
    alerts = [];
    thresholds;
    constructor(engine, logger) {
        super();
        this.engine = engine;
        this.logger = logger;
        this.thresholds = {
            minNetworkEfficiency: 0.6,
            maxAverageLatency: 150,
            minConnectionStrength: 0.3,
            maxWeakConnections: 0.3, // 30% max de connexions faibles
            alertCooldown: 300000 // 5 minutes
        };
        this.setupEngineListeners();
    }
    /**
     * Démarre le monitoring
     */
    async startMonitoring(intervalMs = 30000) {
        if (this.isMonitoring) {
            this.logger.warn('⚠️ Monitoring déjà en cours');
            return;
        }
        this.logger.info('📊 Démarrage du monitoring synaptique');
        this.isMonitoring = true;
        // Collecte périodique des métriques
        this.monitoringInterval = setInterval(async () => {
            await this.collectMetrics();
        }, intervalMs);
        // Collecte initiale
        await this.collectMetrics();
        this.emit('monitoring-started');
    }
    /**
     * Arrête le monitoring
     */
    stopMonitoring() {
        if (!this.isMonitoring) {
            return;
        }
        this.logger.info('🛑 Arrêt du monitoring synaptique');
        this.isMonitoring = false;
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        this.emit('monitoring-stopped');
    }
    /**
     * Collecte les métriques actuelles
     */
    async collectMetrics() {
        try {
            const plasticityMetrics = this.engine.getPlasticityMetrics();
            const communicationPatterns = this.engine.analyzeCommunicationPatterns();
            const suboptimalPaths = this.engine.identifySuboptimalPaths();
            const metrics = {
                timestamp: new Date(),
                plasticity: plasticityMetrics,
                patterns: {
                    totalPatterns: communicationPatterns.length,
                    efficientPatterns: communicationPatterns.filter(p => p.efficiency > 0.7).length,
                    averageEfficiency: this.calculateAverageEfficiency(communicationPatterns),
                    topPatterns: communicationPatterns.slice(0, 5)
                },
                optimization: {
                    suboptimalPaths: suboptimalPaths.length,
                    optimizationPotential: this.calculateTotalOptimizationPotential(suboptimalPaths),
                    topBottlenecks: suboptimalPaths.slice(0, 3)
                },
                health: this.calculateNetworkHealth(plasticityMetrics, communicationPatterns)
            };
            this.metricsHistory.push(metrics);
            // Garder seulement les 100 dernières métriques
            if (this.metricsHistory.length > 100) {
                this.metricsHistory = this.metricsHistory.slice(-100);
            }
            // Vérifier les seuils et générer des alertes
            await this.checkThresholds(metrics);
            this.emit('metrics-collected', metrics);
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de la collecte des métriques:', error);
        }
    }
    /**
     * Vérifie les seuils et génère des alertes
     */
    async checkThresholds(metrics) {
        const now = new Date();
        // Vérifier l'efficacité du réseau
        if (metrics.plasticity.networkEfficiency < this.thresholds.minNetworkEfficiency) {
            await this.createAlert({
                type: 'network_efficiency',
                severity: 'warning',
                message: `Efficacité du réseau faible: ${metrics.plasticity.networkEfficiency.toFixed(3)}`,
                value: metrics.plasticity.networkEfficiency,
                threshold: this.thresholds.minNetworkEfficiency,
                timestamp: now
            });
        }
        // Vérifier la latence moyenne
        if (metrics.plasticity.averageLatency > this.thresholds.maxAverageLatency) {
            await this.createAlert({
                type: 'high_latency',
                severity: 'warning',
                message: `Latence moyenne élevée: ${metrics.plasticity.averageLatency.toFixed(1)}ms`,
                value: metrics.plasticity.averageLatency,
                threshold: this.thresholds.maxAverageLatency,
                timestamp: now
            });
        }
        // Vérifier le pourcentage de connexions faibles
        const weakConnectionRatio = metrics.plasticity.weakConnections / metrics.plasticity.totalConnections;
        if (weakConnectionRatio > this.thresholds.maxWeakConnections) {
            await this.createAlert({
                type: 'weak_connections',
                severity: 'critical',
                message: `Trop de connexions faibles: ${(weakConnectionRatio * 100).toFixed(1)}%`,
                value: weakConnectionRatio,
                threshold: this.thresholds.maxWeakConnections,
                timestamp: now
            });
        }
    }
    /**
     * Crée une alerte
     */
    async createAlert(alert) {
        // Vérifier le cooldown
        const recentAlert = this.alerts.find(a => a.type === alert.type &&
            (alert.timestamp.getTime() - a.timestamp.getTime()) < this.thresholds.alertCooldown);
        if (recentAlert) {
            return; // Alerte en cooldown
        }
        this.alerts.push(alert);
        // Garder seulement les 50 dernières alertes
        if (this.alerts.length > 50) {
            this.alerts = this.alerts.slice(-50);
        }
        this.logger.warn(`🚨 Alerte ${alert.severity}: ${alert.message}`);
        this.emit('alert-created', alert);
        // Déclencher une optimisation automatique si critique
        if (alert.severity === 'critical') {
            this.logger.info('🔧 Déclenchement d\'optimisation automatique');
            await this.engine.optimizeCommunicationPaths();
        }
    }
    /**
     * Configure les listeners sur le moteur
     */
    setupEngineListeners() {
        this.engine.on('connection-created', (connection) => {
            this.emit('connection-event', {
                type: 'created',
                connection,
                timestamp: new Date()
            });
        });
        this.engine.on('connection-pruned', (event) => {
            this.emit('connection-event', {
                type: 'pruned',
                event,
                timestamp: new Date()
            });
        });
        this.engine.on('adaptation-applied', (adaptation) => {
            this.emit('adaptation-event', {
                adaptation,
                timestamp: new Date()
            });
        });
        this.engine.on('paths-optimized', (result) => {
            this.emit('optimization-event', {
                result,
                timestamp: new Date()
            });
        });
    }
    /**
     * Calcule l'efficacité moyenne des patterns
     */
    calculateAverageEfficiency(patterns) {
        if (patterns.length === 0)
            return 0;
        const totalEfficiency = patterns.reduce((sum, pattern) => sum + pattern.efficiency, 0);
        return totalEfficiency / patterns.length;
    }
    /**
     * Calcule le potentiel d'optimisation total
     */
    calculateTotalOptimizationPotential(paths) {
        if (paths.length === 0)
            return 0;
        const totalPotential = paths.reduce((sum, path) => sum + path.optimizationPotential, 0);
        return totalPotential / paths.length;
    }
    /**
     * Calcule la santé globale du réseau
     */
    calculateNetworkHealth(plasticity, patterns) {
        const efficiencyScore = Math.min(1, plasticity.networkEfficiency / 0.8);
        const latencyScore = Math.max(0, 1 - (plasticity.averageLatency / 200));
        const connectionScore = plasticity.totalConnections > 0 ?
            (plasticity.strongConnections / plasticity.totalConnections) : 0;
        const patternScore = patterns.length > 0 ?
            this.calculateAverageEfficiency(patterns) : 0;
        const overallScore = (efficiencyScore + latencyScore + connectionScore + patternScore) / 4;
        return {
            score: overallScore,
            status: this.getHealthStatus(overallScore),
            components: {
                efficiency: efficiencyScore,
                latency: latencyScore,
                connections: connectionScore,
                patterns: patternScore
            }
        };
    }
    /**
     * Détermine le statut de santé
     */
    getHealthStatus(score) {
        if (score >= 0.8)
            return 'excellent';
        if (score >= 0.6)
            return 'good';
        if (score >= 0.4)
            return 'fair';
        if (score >= 0.2)
            return 'poor';
        return 'critical';
    }
    /**
     * Obtient les métriques actuelles
     */
    getCurrentMetrics() {
        if (this.metricsHistory.length > 0) {
            return this.metricsHistory[this.metricsHistory.length - 1];
        }
        return null;
    }
    /**
     * Obtient l'historique des métriques
     */
    getMetricsHistory(limit) {
        return limit ? this.metricsHistory.slice(-limit) : [...this.metricsHistory];
    }
    /**
     * Obtient les alertes actives
     */
    getActiveAlerts() {
        const now = new Date();
        const activeThreshold = 24 * 60 * 60 * 1000; // 24 heures
        return this.alerts.filter(alert => (now.getTime() - alert.timestamp.getTime()) < activeThreshold);
    }
    /**
     * Génère un rapport de santé
     */
    generateHealthReport() {
        const currentMetrics = this.getCurrentMetrics();
        const activeAlerts = this.getActiveAlerts();
        if (!currentMetrics) {
            return {
                timestamp: new Date(),
                status: 'unknown',
                summary: 'Aucune métrique disponible',
                metrics: null,
                alerts: [],
                recommendations: ['Démarrer le monitoring pour obtenir des métriques']
            };
        }
        const recommendations = this.generateRecommendations(currentMetrics, activeAlerts);
        return {
            timestamp: new Date(),
            status: currentMetrics.health.status,
            summary: this.generateSummary(currentMetrics),
            metrics: currentMetrics,
            alerts: activeAlerts,
            recommendations
        };
    }
    /**
     * Génère des recommandations
     */
    generateRecommendations(metrics, alerts) {
        const recommendations = [];
        if (metrics.plasticity.networkEfficiency < 0.6) {
            recommendations.push('Optimiser les voies de communication pour améliorer l\'efficacité');
        }
        if (metrics.plasticity.averageLatency > 100) {
            recommendations.push('Réduire la latence des connexions critiques');
        }
        if (metrics.optimization.suboptimalPaths > 5) {
            recommendations.push('Lancer une optimisation automatique des voies sous-optimales');
        }
        if (alerts.filter(a => a.severity === 'critical').length > 0) {
            recommendations.push('Traiter immédiatement les alertes critiques');
        }
        if (recommendations.length === 0) {
            recommendations.push('Le réseau fonctionne de manière optimale');
        }
        return recommendations;
    }
    /**
     * Génère un résumé
     */
    generateSummary(metrics) {
        const { plasticity, health } = metrics;
        return `Réseau synaptique avec ${plasticity.totalConnections} connexions, ` +
            `efficacité de ${(plasticity.networkEfficiency * 100).toFixed(1)}%, ` +
            `latence moyenne de ${plasticity.averageLatency.toFixed(1)}ms. ` +
            `Santé globale: ${health.status} (${(health.score * 100).toFixed(1)}%).`;
    }
}
exports.NeuroplasticityDashboard = NeuroplasticityDashboard;
//# sourceMappingURL=NeuroplasticityDashboard.js.map