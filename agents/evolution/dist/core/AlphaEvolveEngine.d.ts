import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { EvolutionResult, EvolutionConfig, AlphaEvolveRequest } from '../types/evolution';
/**
 * AlphaEvolve Engine - Moteur d'Évolution Algorithmique
 *
 * Implémente le framework AlphaEvolve pour la découverte et l'optimisation
 * automatique d'algorithmes via l'évolution guidée par LLM.
 *
 * Architecture multi-modèles :
 * - Explorer (breadth) : Génération rapide de variantes
 * - Optimizer (depth) : Analyse approfondie et amélioration
 * - Evaluator : Tests et notation automatisés
 */
export declare class AlphaEvolveEngine extends EventEmitter {
    private logger;
    private config;
    private currentPopulation;
    private generationHistory;
    private isRunning;
    private explorerAgent;
    private optimizerAgent;
    private evaluatorAgent;
    constructor(config: EvolutionConfig, logger: Logger);
    /**
     * Lance le processus d'évolution AlphaEvolve
     */
    evolve(request: AlphaEvolveRequest): Promise<EvolutionResult>;
    /**
     * Génère la population initiale via l'agent Explorer
     */
    private generateInitialPopulation;
    /**
     * Évalue toutes les solutions de la population
     */
    private evaluatePopulation;
    /**
     * Sélectionne les meilleures solutions (élites)
     */
    private selectElites;
    /**
     * Optimise les solutions élites via l'agent Optimizer
     */
    private optimizeElites;
    /**
     * Génère de nouvelles solutions par mutation
     */
    private generateMutations;
    /**
     * Crée une nouvelle population
     */
    private createNewPopulation;
    /**
     * Vérifie la convergence de l'évolution
     */
    private checkConvergence;
    /**
     * Calcule la diversité génétique de la population
     */
    private calculateDiversity;
    /**
     * Sélectionne le type de mutation à appliquer
     */
    private selectMutationType;
    /**
     * Génère une mutation spécifique
     */
    private generateMutation;
    /**
     * Crée le résultat final de l'évolution
     */
    private createEvolutionResult;
    /**
     * Arrête le processus d'évolution
     */
    stop(): void;
}
//# sourceMappingURL=AlphaEvolveEngine.d.ts.map