import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { TechRadarItem, EvolutionPlan, UXTrend, AutoDeployment, AgentConfig } from '../types';
import { EvolutionRequest, EvolutionResult } from '../types/evolution';
/**
 * Agent Evolution - Système Nerveux Adaptatif de l'Organisme IA
 *
 * Inspiré du biomimétisme et du framework AlphaEvolve, cet agent implémente :
 * - Neuroplasticité : Adaptation continue des connexions synaptiques
 * - Évolution darwinienne : Sélection naturelle des meilleures solutions
 * - Système immunitaire : Auto-réparation et détection d'anomalies
 * - ADN/ARN algorithmique : Stockage et transmission des "gènes" de code
 */
export declare class EvolutionAgent extends EventEmitter {
    private logger;
    private config;
    private techRadarEngine;
    private evolutionPlanningEngine;
    private uxTrendEngine;
    private autoDeploymentEngine;
    private continuousLearningEngine;
    private systemOptimizationEngine;
    private alphaEvolveEngine;
    private evolutionaryAlgorithmEngine;
    private neuroplasticityEngine;
    private geneticMemoryEngine;
    private memory;
    private communication;
    private isInitialized;
    constructor(config: AgentConfig, logger: Logger);
    /**
     * Initialise l'agent
     */
    initialize(): Promise<void>;
    /**
     * Lance une évolution AlphaEvolve pour optimiser un algorithme
     */
    evolveAlgorithm(request: EvolutionRequest): Promise<EvolutionResult>;
    /**
     * Adapte les connexions neuroplastiques entre agents
     */
    adaptNeuralConnections(evolutionResult: EvolutionResult): Promise<void>;
    /**
     * Extrait et stocke les gènes des solutions performantes
     */
    extractAndStoreGenes(solutions: EvolutionSolution[]): Promise<void>;
    /**
     * Met à jour le tech radar
     */
    updateTechRadar(): Promise<TechRadarItem[]>;
    /**
     * Analyse les tendances UX
     */
    analyzeUXTrends(): Promise<UXTrend[]>;
    /**
     * Crée un plan d'évolution
     */
    createEvolutionPlan(requirements: any): Promise<EvolutionPlan>;
    /**
     * Exécute un plan d'évolution
     */
    executeEvolutionPlan(planId: string): Promise<void>;
    /**
     * Configure l'auto-déploiement
     */
    setupAutoDeployment(config: any): Promise<AutoDeployment>;
    /**
     * Optimise le système
     */
    optimizeSystem(): Promise<any>;
    /**
     * Apprentissage continu
     */
    performContinuousLearning(): Promise<void>;
    /**
     * Surveille l'écosystème
     */
    monitorEcosystem(): Promise<any>;
    /**
     * Génère des recommandations d'évolution
     */
    generateEvolutionRecommendations(): Promise<any[]>;
    /**
     * Démarre les processus continus
     */
    private startContinuousProcesses;
    /**
     * Surveille l'exécution d'un plan d'évolution
     */
    private monitorEvolutionExecution;
    /**
     * Configure les listeners d'événements
     */
    private setupEventListeners;
    /**
     * Configure les listeners pour les événements AlphaEvolve
     */
    private setupAlphaEvolveListeners;
    /**
     * Démarre les processus de neuroplasticité
     */
    private startNeuroplasticityProcesses;
    /**
     * Analyse les patterns de succès dans les résultats d'évolution
     */
    private analyzeSuccessPatterns;
    /**
     * Arrête l'agent
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=EvolutionAgent.d.ts.map