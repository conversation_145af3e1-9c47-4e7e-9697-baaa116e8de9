"use strict";
/**
 * Configuration globale pour les tests Jest
 * Agent Évolution AlphaEvolve
 */
Object.defineProperty(exports, "__esModule", { value: true });
// Configuration des timeouts pour les tests longs
jest.setTimeout(30000);
// Pas de mock Winston nécessaire - nous utilisons notre propre logger
// Mock des modules externes si nécessaire
jest.mock('fs', () => ({
    promises: {
        readFile: jest.fn(),
        writeFile: jest.fn(),
        mkdir: jest.fn(),
        access: jest.fn()
    },
    existsSync: jest.fn(),
    readFileSync: jest.fn(),
    writeFileSync: jest.fn()
}));
// Configuration des variables d'environnement pour les tests
process.env.NODE_ENV = 'test';
process.env.LOG_LEVEL = 'error'; // Réduire les logs pendant les tests
// Suppression des warnings de deprecation pendant les tests
process.env.NO_DEPRECATION = 'true';
// Configuration globale pour les tests d'évolution
global.testConfig = {
    evolution: {
        populationSize: 5, // Réduit pour les tests
        maxGenerations: 3, // Réduit pour les tests
        convergenceThreshold: 0.1,
        timeout: 10000 // 10 secondes max par test
    }
};
// Utilitaires de test globaux
global.createMockSolution = (overrides = {}) => ({
    id: `test-solution-${Date.now()}`,
    code: 'function test() { return true; }',
    description: 'Test solution',
    approach: 'test-approach',
    fitness: {
        total: 0.5,
        performance: 0.5,
        correctness: 0.5,
        efficiency: 0.5,
        robustness: 0.5,
        maintainability: 0.5,
        innovation: 0.5
    },
    generation: 0,
    parentIds: [],
    mutations: [],
    performance: {
        executionTime: 100,
        memoryUsage: 1024,
        cpuUsage: 0.1,
        complexity: {
            timeComplexity: 'O(1)',
            spaceComplexity: 'O(1)',
            cyclomaticComplexity: 1,
            cognitiveComplexity: 1
        },
        scalability: 0.5
    },
    ...overrides
});
global.createMockRequest = (overrides = {}) => ({
    problem: 'Test problem',
    domain: 'test',
    metrics: ['performance', 'correctness'],
    constraints: {},
    config: {
        populationSize: 5,
        eliteRatio: 0.3,
        mutationRate: 0.1,
        crossoverRate: 0.7,
        maxGenerations: 3,
        convergenceThreshold: 0.1,
        fitnessWeights: {
            performance: 0.3,
            correctness: 0.3,
            efficiency: 0.2,
            robustness: 0.1,
            maintainability: 0.05,
            innovation: 0.05
        },
        selectionStrategy: 'tournament'
    },
    ...overrides
});
// Nettoyage après chaque test
afterEach(() => {
    jest.clearAllMocks();
});
// Nettoyage après tous les tests
afterAll(() => {
    jest.restoreAllMocks();
});
// Gestion des erreurs non capturées pendant les tests
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
});
// Configuration des matchers Jest personnalisés
expect.extend({
    toBeValidFitnessScore(received) {
        const pass = typeof received === 'number' && received >= 0 && received <= 1;
        if (pass) {
            return {
                message: () => `expected ${received} not to be a valid fitness score`,
                pass: true,
            };
        }
        else {
            return {
                message: () => `expected ${received} to be a valid fitness score (0-1)`,
                pass: false,
            };
        }
    },
    toBeValidEvolutionSolution(received) {
        const requiredFields = ['id', 'code', 'description', 'approach', 'fitness', 'generation', 'parentIds', 'mutations', 'performance'];
        const hasAllFields = requiredFields.every(field => received.hasOwnProperty(field));
        if (hasAllFields) {
            return {
                message: () => `expected solution not to have all required fields`,
                pass: true,
            };
        }
        else {
            const missingFields = requiredFields.filter(field => !received.hasOwnProperty(field));
            return {
                message: () => `expected solution to have all required fields. Missing: ${missingFields.join(', ')}`,
                pass: false,
            };
        }
    },
    toHaveImprovedFitness(received, original) {
        const pass = received.fitness.total >= original.fitness.total;
        if (pass) {
            return {
                message: () => `expected fitness ${received.fitness.total} not to be improved from ${original.fitness.total}`,
                pass: true,
            };
        }
        else {
            return {
                message: () => `expected fitness ${received.fitness.total} to be improved from ${original.fitness.total}`,
                pass: false,
            };
        }
    }
});
//# sourceMappingURL=setup.js.map