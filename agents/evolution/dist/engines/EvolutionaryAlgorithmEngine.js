"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EvolutionaryAlgorithmEngine = void 0;
const events_1 = require("events");
const evolution_1 = require("../types/evolution");
/**
 * Moteur d'Algorithmes Évolutionnaires
 *
 * Implémente les opérateurs génétiques classiques :
 * - Sélection (tournament, roulette, rank-based)
 * - Croisement (crossover)
 * - Mutation (simple, créative, optimisation)
 * - Élitisme et préservation de la diversité
 */
class EvolutionaryAlgorithmEngine extends events_1.EventEmitter {
    logger;
    geneticPool = new Map();
    mutationHistory = [];
    constructor(logger) {
        super();
        this.logger = logger;
    }
    /**
     * Initialise le moteur évolutionnaire
     */
    async initialize() {
        this.logger.info('🧬 Initialisation du moteur d\'algorithmes évolutionnaires');
        // Chargement du pool génétique existant
        await this.loadGeneticPool();
        this.logger.info(`✅ Pool génétique chargé: ${this.geneticPool.size} gènes`);
    }
    /**
     * Sélection des parents selon la stratégie choisie
     */
    selectParents(population, strategy, count) {
        switch (strategy) {
            case evolution_1.SelectionStrategy.TOURNAMENT:
                return this.tournamentSelection(population, count);
            case evolution_1.SelectionStrategy.ROULETTE:
                return this.rouletteSelection(population, count);
            case evolution_1.SelectionStrategy.RANK_BASED:
                return this.rankBasedSelection(population, count);
            case evolution_1.SelectionStrategy.ELITIST:
                return this.elitistSelection(population, count);
            case evolution_1.SelectionStrategy.DIVERSITY_PRESERVING:
                return this.diversityPreservingSelection(population, count);
            default:
                return this.tournamentSelection(population, count);
        }
    }
    /**
     * Sélection par tournoi
     */
    tournamentSelection(population, count) {
        const selected = [];
        const tournamentSize = Math.max(2, Math.floor(population.length * 0.1));
        for (let i = 0; i < count; i++) {
            // Sélection aléatoire des participants au tournoi
            const tournament = [];
            for (let j = 0; j < tournamentSize; j++) {
                const randomIndex = Math.floor(Math.random() * population.length);
                tournament.push(population[randomIndex]);
            }
            // Sélection du meilleur du tournoi
            const winner = tournament.reduce((best, current) => current.fitness.total > best.fitness.total ? current : best);
            selected.push(winner);
        }
        return selected;
    }
    /**
     * Sélection par roulette (proportionnelle au fitness)
     */
    rouletteSelection(population, count) {
        const selected = [];
        // Calcul de la fitness totale
        const totalFitness = population.reduce((sum, sol) => sum + sol.fitness.total, 0);
        // Calcul des probabilités cumulatives
        const cumulativeProbabilities = [];
        let cumulative = 0;
        for (const solution of population) {
            cumulative += solution.fitness.total / totalFitness;
            cumulativeProbabilities.push(cumulative);
        }
        // Sélection par roulette
        for (let i = 0; i < count; i++) {
            const random = Math.random();
            const selectedIndex = cumulativeProbabilities.findIndex(prob => random <= prob);
            selected.push(population[selectedIndex]);
        }
        return selected;
    }
    /**
     * Sélection basée sur le rang
     */
    rankBasedSelection(population, count) {
        // Tri par fitness
        const ranked = [...population].sort((a, b) => b.fitness.total - a.fitness.total);
        // Attribution des rangs (meilleur = rang le plus élevé)
        const totalRank = (ranked.length * (ranked.length + 1)) / 2;
        const selected = [];
        for (let i = 0; i < count; i++) {
            const random = Math.random() * totalRank;
            let cumulative = 0;
            for (let j = 0; j < ranked.length; j++) {
                cumulative += ranked.length - j;
                if (random <= cumulative) {
                    selected.push(ranked[j]);
                    break;
                }
            }
        }
        return selected;
    }
    /**
     * Sélection élitiste
     */
    elitistSelection(population, count) {
        return [...population]
            .sort((a, b) => b.fitness.total - a.fitness.total)
            .slice(0, count);
    }
    /**
     * Sélection préservant la diversité
     */
    diversityPreservingSelection(population, count) {
        const selected = [];
        const remaining = [...population];
        // Sélection du meilleur d'abord
        const best = remaining.reduce((best, current) => current.fitness.total > best.fitness.total ? current : best);
        selected.push(best);
        remaining.splice(remaining.indexOf(best), 1);
        // Sélection des autres en maximisant la diversité
        while (selected.length < count && remaining.length > 0) {
            let mostDiverse = remaining[0];
            let maxDiversity = 0;
            for (const candidate of remaining) {
                const diversity = this.calculateDiversityScore(candidate, selected);
                if (diversity > maxDiversity) {
                    maxDiversity = diversity;
                    mostDiverse = candidate;
                }
            }
            selected.push(mostDiverse);
            remaining.splice(remaining.indexOf(mostDiverse), 1);
        }
        return selected;
    }
    /**
     * Croisement de deux solutions parents
     */
    crossover(parent1, parent2) {
        // Analyse des codes parents pour identifier les blocs fonctionnels
        const blocks1 = this.extractCodeBlocks(parent1.code);
        const blocks2 = this.extractCodeBlocks(parent2.code);
        // Génération d'enfants par échange de blocs
        const child1Code = this.combineCodeBlocks(blocks1, blocks2, 0.6);
        const child2Code = this.combineCodeBlocks(blocks2, blocks1, 0.6);
        const child1 = {
            id: `child-${Date.now()}-1`,
            code: child1Code,
            description: `Croisement de ${parent1.id} et ${parent2.id}`,
            approach: `Hybride: ${parent1.approach} + ${parent2.approach}`,
            fitness: { total: 0, performance: 0, correctness: 0, efficiency: 0, robustness: 0, maintainability: 0, innovation: 0 },
            generation: Math.max(parent1.generation, parent2.generation) + 1,
            parentIds: [parent1.id, parent2.id],
            mutations: [],
            performance: { executionTime: 0, memoryUsage: 0, cpuUsage: 0, complexity: { timeComplexity: '', spaceComplexity: '', cyclomaticComplexity: 0, cognitiveComplexity: 0 }, scalability: 0 }
        };
        const child2 = {
            id: `child-${Date.now()}-2`,
            code: child2Code,
            description: `Croisement de ${parent2.id} et ${parent1.id}`,
            approach: `Hybride: ${parent2.approach} + ${parent1.approach}`,
            fitness: { total: 0, performance: 0, correctness: 0, efficiency: 0, robustness: 0, maintainability: 0, innovation: 0 },
            generation: Math.max(parent1.generation, parent2.generation) + 1,
            parentIds: [parent2.id, parent1.id],
            mutations: [],
            performance: { executionTime: 0, memoryUsage: 0, cpuUsage: 0, complexity: { timeComplexity: '', spaceComplexity: '', cyclomaticComplexity: 0, cognitiveComplexity: 0 }, scalability: 0 }
        };
        return [child1, child2];
    }
    /**
     * Mutation d'une solution
     */
    mutate(solution, type, intensity = 0.1) {
        let mutatedCode = solution.code;
        let mutationDescription = '';
        switch (type) {
            case evolution_1.MutationType.SIMPLE:
                mutatedCode = this.simpleMutation(solution.code, intensity);
                mutationDescription = 'Mutation simple de paramètres';
                break;
            case evolution_1.MutationType.OPTIMIZATION:
                mutatedCode = this.optimizationMutation(solution.code);
                mutationDescription = 'Mutation d\'optimisation';
                break;
            case evolution_1.MutationType.REFACTORING:
                mutatedCode = this.refactoringMutation(solution.code);
                mutationDescription = 'Mutation de refactoring';
                break;
            case evolution_1.MutationType.CREATIVE:
                mutatedCode = this.creativeMutation(solution.code);
                mutationDescription = 'Mutation créative';
                break;
        }
        const mutation = {
            id: `mut-${Date.now()}`,
            type,
            description: mutationDescription,
            impact: intensity,
            success: true, // À évaluer plus tard
            parentSolutionId: solution.id
        };
        const mutatedSolution = {
            ...solution,
            id: `mut-${solution.id}-${Date.now()}`,
            code: mutatedCode,
            description: `${solution.description} (${mutationDescription})`,
            generation: solution.generation + 1,
            parentIds: [solution.id],
            mutations: [...solution.mutations, mutation]
        };
        this.mutationHistory.push(mutation);
        return mutatedSolution;
    }
    /**
     * Extraction des blocs de code fonctionnels
     */
    extractCodeBlocks(code) {
        // Implémentation simplifiée - à améliorer avec AST
        const lines = code.split('\n');
        const blocks = [];
        let currentBlock = '';
        let braceCount = 0;
        for (const line of lines) {
            currentBlock += line + '\n';
            braceCount += (line.match(/{/g) || []).length;
            braceCount -= (line.match(/}/g) || []).length;
            if (braceCount === 0 && currentBlock.trim()) {
                blocks.push(currentBlock.trim());
                currentBlock = '';
            }
        }
        if (currentBlock.trim()) {
            blocks.push(currentBlock.trim());
        }
        return blocks;
    }
    /**
     * Combinaison de blocs de code
     */
    combineCodeBlocks(blocks1, blocks2, ratio) {
        const combined = [];
        const maxLength = Math.max(blocks1.length, blocks2.length);
        for (let i = 0; i < maxLength; i++) {
            if (Math.random() < ratio && i < blocks1.length) {
                combined.push(blocks1[i]);
            }
            else if (i < blocks2.length) {
                combined.push(blocks2[i]);
            }
        }
        return combined.join('\n\n');
    }
    /**
     * Mutations spécialisées
     */
    simpleMutation(code, intensity) {
        // Mutation simple : modification de constantes numériques
        return code.replace(/\b\d+\b/g, (match) => {
            if (Math.random() < intensity) {
                const value = parseInt(match);
                const variation = Math.floor(value * 0.1 * (Math.random() - 0.5));
                return (value + variation).toString();
            }
            return match;
        });
    }
    optimizationMutation(code) {
        // Mutation d'optimisation : application de patterns d'optimisation
        let optimized = code;
        // Exemple : optimisation de boucles
        optimized = optimized.replace(/for\s*\(\s*let\s+(\w+)\s*=\s*0\s*;\s*\1\s*<\s*(\w+)\.length\s*;\s*\1\+\+\s*\)/g, 'for (const $1 of $2)');
        return optimized;
    }
    refactoringMutation(code) {
        // Mutation de refactoring : amélioration de la structure
        return code; // Implémentation simplifiée
    }
    creativeMutation(code) {
        // Mutation créative : introduction de nouvelles approches
        return code; // Implémentation simplifiée
    }
    /**
     * Calcul du score de diversité
     */
    calculateDiversityScore(candidate, selected) {
        if (selected.length === 0)
            return 1;
        let totalDistance = 0;
        for (const solution of selected) {
            totalDistance += this.calculateCodeDistance(candidate.code, solution.code);
        }
        return totalDistance / selected.length;
    }
    /**
     * Calcul de la distance entre deux codes
     */
    calculateCodeDistance(code1, code2) {
        // Implémentation simplifiée basée sur la différence de longueur et de mots-clés
        const words1 = new Set(code1.split(/\W+/));
        const words2 = new Set(code2.split(/\W+/));
        const intersection = new Set([...words1].filter(x => words2.has(x)));
        const union = new Set([...words1, ...words2]);
        return 1 - (intersection.size / union.size);
    }
    /**
     * Chargement du pool génétique
     */
    async loadGeneticPool() {
        // Implémentation du chargement depuis la base de données
        // Pour l'instant, initialisation avec des gènes de base
        this.initializeBasicGenes();
    }
    /**
     * Initialisation des gènes de base
     */
    initializeBasicGenes() {
        const basicGenes = [
            { pattern: 'for-loop', code: 'for (let i = 0; i < n; i++)' },
            { pattern: 'binary-search', code: 'while (left <= right) { mid = Math.floor((left + right) / 2); }' },
            { pattern: 'memoization', code: 'const memo = new Map(); if (memo.has(key)) return memo.get(key);' }
        ];
        basicGenes.forEach((gene, index) => {
            this.geneticPool.set(`gene-${index}`, {
                id: `gene-${index}`,
                sequence: gene.code,
                type: 'algorithm',
                expression: gene.pattern,
                fitness: 0.5,
                age: 0,
                usageCount: 0,
                lastUsed: new Date()
            });
        });
    }
}
exports.EvolutionaryAlgorithmEngine = EvolutionaryAlgorithmEngine;
//# sourceMappingURL=EvolutionaryAlgorithmEngine.js.map