import { Logger } from 'winston';
import { AgentConfig, TechRadarItem } from '../types';
/**
 * Moteur Tech Radar - Suivi des tendances technologiques
 */
export declare class TechRadarEngine {
    private logger;
    private config;
    private octokit?;
    private isRunning;
    private updateTask?;
    constructor(config: AgentConfig, logger: Logger);
    /**
     * Initialise le moteur
     */
    initialize(): Promise<void>;
    /**
     * Collecte les tendances technologiques
     */
    collectTechTrends(): Promise<any[]>;
    /**
     * Évalue les technologies
     */
    evaluateTechnologies(trends: any[]): Promise<TechRadarItem[]>;
    /**
     * Met à jour le radar technologique
     */
    updateRadar(technologies: TechRadarItem[]): Promise<TechRadarItem[]>;
    /**
     * Démarre les mises à jour continues
     */
    startContinuousUpdates(): Promise<void>;
    /**
     * Arrête les mises à jour continues
     */
    stopContinuousUpdates(): Promise<void>;
    /**
     * Traite les données de recherche web
     */
    processWebResearchData(data: any): Promise<void>;
    /**
     * Collecte les tendances GitHub
     */
    private collectGitHubTrends;
    /**
     * Collecte les tendances NPM
     */
    private collectNPMTrends;
    /**
     * Collecte les tendances Stack Overflow
     */
    private collectStackOverflowTrends;
    /**
     * Collecte les tendances web
     */
    private collectWebTrends;
    /**
     * Évalue une technologie individuelle
     */
    private evaluateTechnology;
    /**
     * Calcule le score d'une technologie
     */
    private calculateTechScore;
    /**
     * Effectue une mise à jour programmée
     */
    private performScheduledUpdate;
    private categorizeTechnology;
    private determineQuadrant;
    private determineRing;
    private determineStatus;
    private determineTrend;
    private determineImpact;
    private calculateAdoptionLevel;
    private calculateMaturityLevel;
    private calculateRiskLevel;
    private calculateTrendScore;
    private analyzePositionChanges;
    private identifyNewTechnologies;
    private updateTechStatuses;
    private extractTechMentions;
    private analyzeTechTrends;
    private updateAdoptionScores;
    private generateId;
}
//# sourceMappingURL=TechRadarEngine.d.ts.map