import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { GeneticCode, GeneticMemoryRequest, EvolutionSolution } from '../types/evolution';
/**
 * Moteur de Mémoire Génétique
 *
 * Implémente un système de mémoire inspiré de l'ADN/ARN pour stocker,
 * récupérer et faire évoluer les "gènes" algorithmiques :
 * - Stockage de patterns de code réussis
 * - Indexation sémantique des solutions
 * - Évolution des gènes par recombinaison
 * - Hérédité des caractéristiques performantes
 * - Mutation contrôlée des gènes
 */
export declare class GeneticMemoryEngine extends EventEmitter {
    private logger;
    private genePool;
    private geneIndex;
    private geneLineage;
    private expressionHistory;
    private fitnessThreshold;
    constructor(logger: Logger);
    /**
     * Initialise le moteur de mémoire génétique
     */
    initialize(): Promise<void>;
    /**
     * Traite une demande de mémoire génétique
     */
    processRequest(request: GeneticMemoryRequest): Promise<any>;
    /**
     * Stocke un nouveau gène dans la mémoire
     */
    storeGene(request: GeneticMemoryRequest): Promise<GeneticCode>;
    /**
     * Récupère des gènes selon les critères
     */
    retrieveGenes(request: GeneticMemoryRequest): Promise<GeneticCode[]>;
    /**
     * Fait évoluer les gènes par recombinaison
     */
    evolveGenes(request: GeneticMemoryRequest): Promise<GeneticCode[]>;
    /**
     * Extrait les gènes d'une solution performante
     */
    extractGenesFromSolution(solution: EvolutionSolution): Promise<GeneticCode[]>;
    /**
     * Met à jour la fitness d'un gène
     */
    updateGeneFitness(geneId: string, newFitness: number, context?: any): Promise<void>;
    /**
     * Recherche par pattern sémantique
     */
    private searchByPattern;
    /**
     * Sélectionne les gènes parents pour l'évolution
     */
    private selectParentGenes;
    /**
     * Recombinaison de deux gènes parents
     */
    private recombineGenes;
    /**
     * Mutation des gènes
     */
    private mutateGenes;
    /**
     * Identifie les patterns de code
     */
    private identifyCodePatterns;
    /**
     * Croisement de séquences
     */
    private crossoverSequences;
    /**
     * Mutation d'une séquence
     */
    private mutateSequence;
    /**
     * Extraction de l'expression d'un pattern
     */
    private extractExpression;
    /**
     * Mise à jour de l'index des gènes
     */
    private updateGeneIndex;
    /**
     * Création d'une lignée génétique
     */
    private createGeneLineage;
    /**
     * Enregistrement d'une expression génétique
     */
    private recordGeneExpression;
    /**
     * Calcul de similarité sémantique
     */
    private calculateSemanticSimilarity;
    /**
     * Extraction des termes de recherche
     */
    private extractSearchTerms;
    /**
     * Construction de l'index des gènes
     */
    private buildGeneIndex;
    /**
     * Chargement du pool génétique
     */
    private loadGenePool;
    /**
     * Initialisation des gènes fondamentaux
     */
    private initializeFundamentalGenes;
}
//# sourceMappingURL=GeneticMemoryEngine.d.ts.map