import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { EvolutionSolution, AlphaEvolveRequest } from '../types/evolution';
/**
 * Evaluator Agent - Tests et Notation Automatisés
 *
 * Inspiré du framework AlphaEvolve, cet agent se spécialise dans :
 * - Évaluation objective des solutions
 * - Tests automatisés de correctness
 * - Mesure de performance en temps réel
 * - Calcul de scores de fitness multi-critères
 */
export declare class EvaluatorAgent extends EventEmitter {
    private logger;
    private evaluationHistory;
    private testSuites;
    private benchmarkResults;
    constructor(logger: Logger);
    /**
     * Évalue une solution selon tous les critères de fitness
     */
    evaluateSolution(solution: EvolutionSolution, request: AlphaEvolveRequest): Promise<EvolutionSolution>;
    /**
     * Évalue la correctness d'une solution
     */
    private evaluateCorrectness;
    /**
     * Évalue la performance d'une solution
     */
    private evaluatePerformance;
    /**
     * Évalue la qualité du code
     */
    private evaluateCodeQuality;
    /**
     * Évalue la robustesse d'une solution
     */
    private evaluateRobustness;
    /**
     * Évalue l'innovation d'une solution
     */
    private evaluateInnovation;
    /**
     * Calcule le score de fitness final
     */
    private calculateFitnessScore;
    /**
     * Exécute un test sur une solution
     */
    private executeTest;
    /**
     * Exécute un benchmark sur une solution
     */
    private runBenchmark;
    /**
     * Simulation d'exécution de code (à remplacer par un vrai environnement)
     */
    private simulateCodeExecution;
    /**
     * Compare les résultats attendus et obtenus
     */
    private compareResults;
    /**
     * Méthodes de calcul de métriques
     */
    private calculateReadability;
    private calculateMaintainability;
    private calculateComplexityScore;
    private calculateDocumentationScore;
    private calculateTestability;
    private calculateCyclomaticComplexity;
    private calculateNovelty;
    private calculateCreativity;
    private calculateUniqueness;
    private calculateEfficiencyScore;
    private calculateScalability;
    private calculateBenchmarkScore;
    /**
     * Méthodes utilitaires
     */
    private initializeTestSuites;
    private getTestSuite;
    private getBenchmarks;
    private generateEdgeCases;
    private generateStressTests;
    private executeStressTest;
    private getDefaultFitness;
    private getDefaultPerformance;
    private recordEvaluation;
}
//# sourceMappingURL=EvaluatorAgent.d.ts.map