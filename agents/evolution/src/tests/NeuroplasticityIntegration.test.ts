import { NeuroplasticityEngine } from '../engines/NeuroplasticityEngine';
import { NeuroplasticityDashboard } from '../monitoring/NeuroplasticityDashboard';
import { AdaptationType } from '../types/evolution';
import { SilentLogger } from '../utils/logger';

describe('Neuroplasticity Integration Tests', () => {
  let engine: NeuroplasticityEngine;
  let dashboard: NeuroplasticityDashboard;
  let logger: SilentLogger;

  beforeEach(async () => {
    logger = new SilentLogger();
    engine = new NeuroplasticityEngine(logger);
    dashboard = new NeuroplasticityDashboard(engine, logger);
    
    await engine.initialize();
  });

  afterEach(() => {
    dashboard.stopMonitoring();
  });

  describe('End-to-End Neuroplasticity Workflow', () => {
    test('should complete full adaptation cycle', async () => {
      // 1. Créer des connexions initiales
      await engine.createConnection('agent-frontend', 'agent-backend', { latency: 80 });
      await engine.createConnection('agent-backend', 'agent-database', { latency: 120 });
      await engine.createConnection('agent-frontend', 'agent-cache', { latency: 30 });

      // 2. Simuler des interactions pour renforcer certaines connexions
      for (let i = 0; i < 10; i++) {
        await engine.strengthenConnection('agent-frontend', 'agent-cache', { 
          success: true, 
          latency: 25 
        });
      }

      // 3. Simuler des échecs pour affaiblir d'autres connexions
      for (let i = 0; i < 5; i++) {
        await engine.weakenConnection('agent-backend', 'agent-database', 'timeout');
      }

      // 4. Analyser les patterns de communication
      const patterns = engine.analyzeCommunicationPatterns();
      expect(patterns.length).toBeGreaterThan(0);

      // 5. Identifier les voies sous-optimales
      const suboptimalPaths = engine.identifySuboptimalPaths();
      expect(suboptimalPaths.length).toBeGreaterThan(0);

      // 6. Optimiser automatiquement
      const optimizationResult = await engine.optimizeCommunicationPaths();
      expect(optimizationResult.pathsOptimized).toBeGreaterThan(0);

      // 7. Vérifier l'amélioration des métriques
      const finalMetrics = engine.getPlasticityMetrics();
      expect(finalMetrics.networkEfficiency).toBeGreaterThan(0);
    });

    test('should handle complex network topology', async () => {
      // Créer un réseau complexe avec hub central
      const agents = ['frontend', 'backend', 'database', 'cache', 'auth', 'analytics'];
      const hubAgent = 'cortex-central';

      // Connecter tous les agents au hub
      for (const agent of agents) {
        await engine.createConnection(hubAgent, agent, { latency: 50 });
        await engine.createConnection(agent, hubAgent, { latency: 45 });
      }

      // Créer quelques connexions directes
      await engine.createConnection('frontend', 'backend', { latency: 60 });
      await engine.createConnection('backend', 'database', { latency: 100 });
      await engine.createConnection('frontend', 'cache', { latency: 20 });

      // Analyser la topologie
      const patterns = engine.analyzeCommunicationPatterns();
      const hubPattern = patterns.find(p => p.agents.includes(hubAgent));
      
      expect(hubPattern).toBeDefined();
      expect(hubPattern!.pattern).toBe('hub');

      // Optimiser le réseau
      const result = await engine.optimizeCommunicationPaths();
      expect(result.networkEfficiencyGain).toBeGreaterThan(0);
    });
  });

  describe('Dashboard Integration', () => {
    test('should monitor neuroplasticity in real-time', async () => {
      const metricsCollected: any[] = [];
      const alertsCreated: any[] = [];

      // Écouter les événements du dashboard
      dashboard.on('metrics-collected', (metrics) => {
        metricsCollected.push(metrics);
      });

      dashboard.on('alert-created', (alert) => {
        alertsCreated.push(alert);
      });

      // Démarrer le monitoring avec un intervalle court pour les tests
      await dashboard.startMonitoring(100);

      // Créer des connexions avec des problèmes pour déclencher des alertes
      await engine.createConnection('slow-agent', 'target-agent', { latency: 200 });
      
      // Attendre quelques cycles de collecte
      await new Promise(resolve => setTimeout(resolve, 350));

      dashboard.stopMonitoring();

      // Vérifier que les métriques ont été collectées
      expect(metricsCollected.length).toBeGreaterThan(0);
      
      const lastMetrics = metricsCollected[metricsCollected.length - 1];
      expect(lastMetrics.plasticity).toBeDefined();
      expect(lastMetrics.health).toBeDefined();
      expect(lastMetrics.patterns).toBeDefined();
    });

    test('should generate health reports', async () => {
      // Créer un réseau avec des problèmes
      await engine.createConnection('problematic-agent', 'target', { latency: 300 });
      
      // Démarrer le monitoring brièvement
      await dashboard.startMonitoring(50);
      await new Promise(resolve => setTimeout(resolve, 100));
      dashboard.stopMonitoring();

      // Générer un rapport de santé
      const report = dashboard.generateHealthReport();

      expect(report).toBeDefined();
      expect(report.timestamp).toBeInstanceOf(Date);
      expect(report.status).toBeDefined();
      expect(report.summary).toBeDefined();
      expect(Array.isArray(report.recommendations)).toBe(true);
    });

    test('should trigger automatic optimization on critical alerts', async () => {
      let optimizationTriggered = false;

      // Écouter les événements d'optimisation
      engine.on('paths-optimized', () => {
        optimizationTriggered = true;
      });

      // Créer des connexions très problématiques
      for (let i = 0; i < 10; i++) {
        await engine.createConnection(`weak-agent-${i}`, 'target', { latency: 50 });
        // Affaiblir immédiatement pour créer des connexions faibles
        for (let j = 0; j < 8; j++) {
          await engine.weakenConnection(`weak-agent-${i}`, 'target', 'repeated_failure');
        }
      }

      // Démarrer le monitoring
      await dashboard.startMonitoring(50);
      
      // Attendre que les alertes se déclenchent
      await new Promise(resolve => setTimeout(resolve, 200));
      
      dashboard.stopMonitoring();

      // Vérifier qu'une optimisation automatique a été déclenchée
      expect(optimizationTriggered).toBe(true);
    });
  });

  describe('Performance and Scalability', () => {
    test('should handle large number of connections efficiently', async () => {
      const startTime = Date.now();
      
      // Créer un grand nombre de connexions
      const agentCount = 50;
      const agents = Array.from({ length: agentCount }, (_, i) => `agent-${i}`);

      for (let i = 0; i < agentCount; i++) {
        for (let j = i + 1; j < Math.min(i + 5, agentCount); j++) {
          await engine.createConnection(agents[i], agents[j], { 
            latency: Math.random() * 100 + 20 
          });
        }
      }

      const creationTime = Date.now() - startTime;
      expect(creationTime).toBeLessThan(5000); // Moins de 5 secondes

      // Analyser les patterns
      const analysisStart = Date.now();
      const patterns = engine.analyzeCommunicationPatterns();
      const analysisTime = Date.now() - analysisStart;

      expect(patterns.length).toBeGreaterThan(0);
      expect(analysisTime).toBeLessThan(1000); // Moins d'1 seconde

      // Optimiser
      const optimizationStart = Date.now();
      const result = await engine.optimizeCommunicationPaths();
      const optimizationTime = Date.now() - optimizationStart;

      expect(result.pathsAnalyzed).toBeGreaterThan(0);
      expect(optimizationTime).toBeLessThan(2000); // Moins de 2 secondes
    });

    test('should maintain performance with continuous adaptation', async () => {
      // Créer un réseau de base
      const agents = ['a', 'b', 'c', 'd', 'e'];
      for (let i = 0; i < agents.length; i++) {
        for (let j = i + 1; j < agents.length; j++) {
          await engine.createConnection(agents[i], agents[j], { latency: 50 });
        }
      }

      const adaptationCount = 100;
      const startTime = Date.now();

      // Effectuer de nombreuses adaptations
      for (let i = 0; i < adaptationCount; i++) {
        const fromAgent = agents[Math.floor(Math.random() * agents.length)];
        const toAgent = agents[Math.floor(Math.random() * agents.length)];
        
        if (fromAgent !== toAgent) {
          if (Math.random() > 0.5) {
            await engine.strengthenConnection(fromAgent, toAgent, { success: true });
          } else {
            await engine.weakenConnection(fromAgent, toAgent, 'random_test');
          }
        }
      }

      const totalTime = Date.now() - startTime;
      const avgTimePerAdaptation = totalTime / adaptationCount;

      expect(avgTimePerAdaptation).toBeLessThan(10); // Moins de 10ms par adaptation

      // Vérifier que les métriques sont cohérentes
      const metrics = engine.getPlasticityMetrics();
      expect(metrics.totalConnections).toBeGreaterThan(0);
      expect(metrics.averageStrength).toBeGreaterThanOrEqual(0);
      expect(metrics.averageStrength).toBeLessThanOrEqual(1);
    });
  });

  describe('Error Handling and Resilience', () => {
    test('should handle invalid connection operations gracefully', async () => {
      // Tenter d'affaiblir une connexion inexistante
      await expect(
        engine.weakenConnection('nonexistent-a', 'nonexistent-b', 'test')
      ).resolves.not.toThrow();

      // Tenter de renforcer une connexion inexistante (devrait la créer)
      await expect(
        engine.strengthenConnection('new-a', 'new-b', { success: true })
      ).resolves.not.toThrow();

      const metrics = engine.getPlasticityMetrics();
      expect(metrics.totalConnections).toBe(1);
    });

    test('should recover from dashboard monitoring errors', async () => {
      // Simuler une erreur en corrompant temporairement l'état
      const originalMethod = engine.getPlasticityMetrics;
      engine.getPlasticityMetrics = () => {
        throw new Error('Simulated error');
      };

      // Démarrer le monitoring
      await dashboard.startMonitoring(50);
      
      // Attendre quelques cycles (ne devrait pas planter)
      await new Promise(resolve => setTimeout(resolve, 150));
      
      // Restaurer la méthode
      engine.getPlasticityMetrics = originalMethod;
      
      // Attendre encore un peu
      await new Promise(resolve => setTimeout(resolve, 100));
      
      dashboard.stopMonitoring();

      // Le dashboard devrait toujours fonctionner
      const report = dashboard.generateHealthReport();
      expect(report).toBeDefined();
    });
  });
});
