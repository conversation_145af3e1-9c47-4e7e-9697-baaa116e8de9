# Guide de Déploiement - Agent Évolution AlphaEvolve

## 🎯 Vue d'Ensemble

Ce guide fournit toutes les instructions nécessaires pour déployer l'Agent Évolution AlphaEvolve en production. Le système est conçu pour être déployé dans un environnement Kubernetes avec haute disponibilité, monitoring avancé et scaling automatique.

## 📋 Prérequis

### Infrastructure
- **Kubernetes** 1.24+ avec support des CRDs
- **Docker** 20.10+ pour la containerisation
- **Helm** 3.8+ pour la gestion des packages
- **Ingress Controller** (NGINX ou Traefik)
- **Cert-Manager** pour les certificats SSL

### Ressources Minimales
- **CPU** : 8 cores (16 cores recommandés)
- **Mémoire** : 16 GB RAM (32 GB recommandés)
- **Stockage** : 100 GB SSD (500 GB recommandés)
- **Réseau** : 1 Gbps (10 Gbps recommandés)

### Services Externes
- **Base de données** : PostgreSQL 14+ ou MongoDB 5.0+
- **Cache** : Redis 6.0+ ou Memcached
- **Monitoring** : Prometheus + Grafana
- **Logging** : ELK Stack ou Loki
- **Tracing** : Jaeger ou Zipkin

## 🏗️ Architecture de Déploiement

```
┌─────────────────────────────────────────────────────────────┐
│                    LOAD BALANCER                            │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │ Evolution   │ Evolution   │ Evolution   │ Evolution   │  │
│  │ Agent       │ Agent       │ Agent       │ Agent       │  │
│  │ Pod 1       │ Pod 2       │ Pod 3       │ Pod N       │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │ PostgreSQL  │ Redis       │ Prometheus  │ Grafana     │  │
│  │ Cluster     │ Cluster     │ Stack       │ Dashboard   │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Déploiement Étape par Étape

### 1. Préparation de l'Environnement

#### Configuration du Namespace
```bash
# Création du namespace
kubectl create namespace evolution-agent-prod

# Configuration des labels
kubectl label namespace evolution-agent-prod \
  app=evolution-agent \
  environment=production \
  version=v1.0.0
```

#### Secrets et ConfigMaps
```bash
# Secrets pour la base de données
kubectl create secret generic evolution-db-secret \
  --from-literal=username=evolution_user \
  --from-literal=password=secure_password \
  --from-literal=host=postgres.evolution.svc.cluster.local \
  --from-literal=database=evolution_db \
  -n evolution-agent-prod

# Secrets pour Redis
kubectl create secret generic evolution-redis-secret \
  --from-literal=password=redis_password \
  --from-literal=host=redis.evolution.svc.cluster.local \
  -n evolution-agent-prod

# ConfigMap pour la configuration
kubectl create configmap evolution-config \
  --from-file=config/production.yaml \
  -n evolution-agent-prod
```

### 2. Construction et Push de l'Image Docker

#### Dockerfile Optimisé
```dockerfile
# Multi-stage build pour optimisation
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

# Image de production
FROM node:18-alpine AS production

RUN addgroup -g 1001 -S evolution && \
    adduser -S evolution -u 1001

WORKDIR /app

# Installation des dépendances de sécurité
RUN apk add --no-cache dumb-init

# Copie des fichiers de build
COPY --from=builder --chown=evolution:evolution /app/dist ./dist
COPY --from=builder --chown=evolution:evolution /app/node_modules ./node_modules
COPY --from=builder --chown=evolution:evolution /app/package.json ./

USER evolution

EXPOSE 3000 8080 9090

ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/index.js"]
```

#### Build et Push
```bash
# Build de l'image
docker build -t evolution-agent:v1.0.0 .

# Tag pour le registry
docker tag evolution-agent:v1.0.0 your-registry.com/evolution-agent:v1.0.0

# Push vers le registry
docker push your-registry.com/evolution-agent:v1.0.0
```

### 3. Déploiement Kubernetes

#### Deployment Principal
```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: evolution-agent
  namespace: evolution-agent-prod
  labels:
    app: evolution-agent
    version: v1.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: evolution-agent
  template:
    metadata:
      labels:
        app: evolution-agent
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: evolution-agent
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        fsGroup: 1001
      containers:
      - name: evolution-agent
        image: your-registry.com/evolution-agent:v1.0.0
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
          name: http
        - containerPort: 8080
          name: health
        - containerPort: 9090
          name: metrics
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3000"
        - name: HEALTH_PORT
          value: "8080"
        - name: METRICS_PORT
          value: "9090"
        envFrom:
        - secretRef:
            name: evolution-db-secret
        - secretRef:
            name: evolution-redis-secret
        - configMapRef:
            name: evolution-config
        resources:
          requests:
            cpu: 2000m
            memory: 4Gi
          limits:
            cpu: 4000m
            memory: 8Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 2
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
          readOnly: true
        - name: logs-volume
          mountPath: /var/log/evolution-agent
      volumes:
      - name: config-volume
        configMap:
          name: evolution-config
      - name: logs-volume
        emptyDir: {}
      nodeSelector:
        node-type: compute-optimized
      tolerations:
      - key: "compute-optimized"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
```

#### Service et Ingress
```yaml
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: evolution-agent-service
  namespace: evolution-agent-prod
  labels:
    app: evolution-agent
spec:
  selector:
    app: evolution-agent
  ports:
  - name: http
    port: 80
    targetPort: 3000
  - name: health
    port: 8080
    targetPort: 8080
  - name: metrics
    port: 9090
    targetPort: 9090
  type: ClusterIP

---
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: evolution-agent-ingress
  namespace: evolution-agent-prod
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/rate-limit: "1000"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - evolution-api.retreatandbe.com
    secretName: evolution-agent-tls
  rules:
  - host: evolution-api.retreatandbe.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: evolution-agent-service
            port:
              number: 80
```

### 4. Auto-Scaling Configuration

#### Horizontal Pod Autoscaler
```yaml
# hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: evolution-agent-hpa
  namespace: evolution-agent-prod
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: evolution-agent
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 75
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
```

#### Vertical Pod Autoscaler
```yaml
# vpa.yaml
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: evolution-agent-vpa
  namespace: evolution-agent-prod
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: evolution-agent
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: evolution-agent
      maxAllowed:
        cpu: 8000m
        memory: 16Gi
      minAllowed:
        cpu: 1000m
        memory: 2Gi
```

### 5. Monitoring et Observabilité

#### ServiceMonitor pour Prometheus
```yaml
# servicemonitor.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: evolution-agent-metrics
  namespace: evolution-agent-prod
  labels:
    app: evolution-agent
spec:
  selector:
    matchLabels:
      app: evolution-agent
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
```

#### Grafana Dashboard
```json
{
  "dashboard": {
    "title": "Evolution Agent Dashboard",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(evolution_requests_total[5m])",
            "legendFormat": "{{method}} {{status}}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(evolution_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "Active Evolutions",
        "type": "stat",
        "targets": [
          {
            "expr": "evolution_active_evolutions",
            "legendFormat": "Active"
          }
        ]
      }
    ]
  }
}
```

### 6. Sécurité et Conformité

#### Network Policies
```yaml
# networkpolicy.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: evolution-agent-netpol
  namespace: evolution-agent-prod
spec:
  podSelector:
    matchLabels:
      app: evolution-agent
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 3000
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9090
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: database
    ports:
    - protocol: TCP
      port: 5432
  - to:
    - namespaceSelector:
        matchLabels:
          name: cache
    ports:
    - protocol: TCP
      port: 6379
```

#### Pod Security Policy
```yaml
# psp.yaml
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: evolution-agent-psp
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'
```

## 🔧 Configuration Post-Déploiement

### 1. Validation du Déploiement
```bash
# Vérification des pods
kubectl get pods -n evolution-agent-prod

# Vérification des services
kubectl get svc -n evolution-agent-prod

# Vérification des logs
kubectl logs -f deployment/evolution-agent -n evolution-agent-prod

# Test de santé
curl https://evolution-api.retreatandbe.com/health
```

### 2. Configuration du Monitoring
```bash
# Import du dashboard Grafana
curl -X POST \
  https://grafana.retreatandbe.com/api/dashboards/db \
  -H 'Content-Type: application/json' \
  -d @grafana-dashboard.json

# Configuration des alertes
kubectl apply -f prometheus-rules.yaml
```

### 3. Tests de Charge
```bash
# Test de charge avec k6
k6 run --vus 100 --duration 5m load-test.js

# Test de stress
kubectl apply -f stress-test-job.yaml
```

## 📊 Monitoring et Alertes

### Métriques Clés à Surveiller
- **Throughput** : Requêtes/seconde
- **Latence** : P50, P95, P99 des temps de réponse
- **Taux d'erreur** : Pourcentage d'erreurs 4xx/5xx
- **Utilisation ressources** : CPU, mémoire, réseau
- **Évolutions actives** : Nombre d'évolutions en cours
- **Santé des composants** : Status des services internes

### Alertes Critiques
```yaml
# prometheus-rules.yaml
groups:
- name: evolution-agent.rules
  rules:
  - alert: HighErrorRate
    expr: rate(evolution_requests_total{status=~"5.."}[5m]) > 0.1
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High error rate detected"
      
  - alert: HighLatency
    expr: histogram_quantile(0.95, rate(evolution_request_duration_seconds_bucket[5m])) > 5
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High latency detected"
```

## 🔄 Maintenance et Mise à Jour

### Rolling Updates
```bash
# Mise à jour de l'image
kubectl set image deployment/evolution-agent \
  evolution-agent=your-registry.com/evolution-agent:v1.1.0 \
  -n evolution-agent-prod

# Suivi du rollout
kubectl rollout status deployment/evolution-agent -n evolution-agent-prod

# Rollback si nécessaire
kubectl rollout undo deployment/evolution-agent -n evolution-agent-prod
```

### Backup et Recovery
```bash
# Backup de la configuration
kubectl get all,configmap,secret -n evolution-agent-prod -o yaml > backup.yaml

# Backup de la base de données
pg_dump evolution_db > evolution_db_backup.sql

# Test de recovery
kubectl apply -f backup.yaml
```

## 🚨 Troubleshooting

### Problèmes Courants

#### 1. Pods en CrashLoopBackOff
```bash
# Vérification des logs
kubectl logs -f pod/evolution-agent-xxx -n evolution-agent-prod

# Vérification des événements
kubectl describe pod/evolution-agent-xxx -n evolution-agent-prod

# Vérification des ressources
kubectl top pod -n evolution-agent-prod
```

#### 2. Problèmes de Performance
```bash
# Vérification des métriques
kubectl top nodes
kubectl top pods -n evolution-agent-prod

# Analyse des goulots d'étranglement
kubectl exec -it pod/evolution-agent-xxx -n evolution-agent-prod -- top
```

#### 3. Problèmes de Connectivité
```bash
# Test de connectivité réseau
kubectl exec -it pod/evolution-agent-xxx -n evolution-agent-prod -- nslookup postgres.evolution.svc.cluster.local

# Vérification des Network Policies
kubectl describe networkpolicy -n evolution-agent-prod
```

## 📞 Support et Escalade

### Contacts d'Urgence
- **Niveau 1** : Équipe DevOps (24/7)
- **Niveau 2** : Équipe Développement
- **Niveau 3** : Architecte Système

### Procédures d'Escalade
1. **Incident Critique** : Notification immédiate
2. **Problème Performance** : Escalade sous 30 minutes
3. **Bug Fonctionnel** : Escalade sous 2 heures

---

## ✅ Checklist de Déploiement

- [ ] Infrastructure Kubernetes prête
- [ ] Secrets et ConfigMaps créés
- [ ] Image Docker buildée et pushée
- [ ] Deployment Kubernetes appliqué
- [ ] Services et Ingress configurés
- [ ] Auto-scaling configuré
- [ ] Monitoring et alertes activés
- [ ] Tests de santé passés
- [ ] Tests de charge validés
- [ ] Documentation mise à jour
- [ ] Équipe formée sur les procédures

**🎉 Déploiement Terminé avec Succès !**
