#!/usr/bin/env node

/**
 * Script de Test des Interfaces Émotionnelles d'Hanuman
 * Sprint 4 - Validation Complète
 * 
 * Ce script teste toutes les interfaces émotionnelles créées dans le Sprint 4:
 * - Interface Personnalité
 * - Interface Émotions  
 * - Interface Empathie
 * - Interface Relations Sociales
 * - Connecteur Agents Émotionnels
 */

import { EmotionalAgentConnector } from '../services/EmotionalAgentConnector';
import { 
  EMOTIONAL_ORGANS_CONFIG, 
  AGENT_CONNECTIONS_CONFIG,
  EMOTIONAL_METRICS_CONFIG,
  HanumanUtils 
} from '../index';

interface TestResult {
  testName: string;
  status: 'PASS' | 'FAIL' | 'SKIP';
  duration: number;
  details?: string;
  error?: Error;
}

class EmotionalInterfacesTestSuite {
  private results: TestResult[] = [];
  private connector: EmotionalAgentConnector | null = null;

  constructor() {
    console.log(`
🧪 ===== TEST SUITE INTERFACES ÉMOTIONNELLES ===== 🧪
Sprint 4 - Personnalité et Émotions
Date: ${new Date().toISOString()}
================================================
    `);
  }

  /**
   * Lance tous les tests
   */
  public async runAllTests(): Promise<void> {
    console.log('🚀 Démarrage des tests...\n');

    try {
      // Tests de configuration
      await this.testConfiguration();
      
      // Tests du connecteur émotionnel
      await this.testEmotionalConnector();
      
      // Tests des interfaces individuelles
      await this.testPersonalityInterface();
      await this.testEmotionsInterface();
      await this.testEmpathyInterface();
      await this.testSocialInterface();
      
      // Tests d'intégration
      await this.testIntegration();
      
      // Tests de performance
      await this.testPerformance();

    } catch (error) {
      console.error('❌ Erreur critique dans la suite de tests:', error);
    } finally {
      await this.cleanup();
      this.generateReport();
    }
  }

  /**
   * Test de la configuration
   */
  private async testConfiguration(): Promise<void> {
    console.log('📋 Tests de Configuration...');

    await this.runTest('Configuration des Organes Émotionnels', async () => {
      const organs = Object.keys(EMOTIONAL_ORGANS_CONFIG);
      if (organs.length !== 4) {
        throw new Error(`Attendu 4 organes, trouvé ${organs.length}`);
      }
      
      for (const organKey of organs) {
        const organ = EMOTIONAL_ORGANS_CONFIG[organKey as keyof typeof EMOTIONAL_ORGANS_CONFIG];
        if (!organ.id || !organ.name || !organ.capabilities) {
          throw new Error(`Configuration incomplète pour l'organe ${organKey}`);
        }
      }
      
      return 'Tous les organes émotionnels sont correctement configurés';
    });

    await this.runTest('Configuration des Connexions Agents', async () => {
      const agents = Object.keys(AGENT_CONNECTIONS_CONFIG);
      if (agents.length !== 5) {
        throw new Error(`Attendu 5 agents, trouvé ${agents.length}`);
      }
      
      for (const agentKey of agents) {
        const agent = AGENT_CONNECTIONS_CONFIG[agentKey as keyof typeof AGENT_CONNECTIONS_CONFIG];
        if (!agent.id || !agent.host || !agent.port) {
          throw new Error(`Configuration incomplète pour l'agent ${agentKey}`);
        }
      }
      
      return 'Tous les agents sont correctement configurés';
    });

    await this.runTest('Configuration des Métriques', async () => {
      const metricsKeys = Object.keys(EMOTIONAL_METRICS_CONFIG);
      let totalMetrics = 0;
      
      for (const key of metricsKeys) {
        const metrics = EMOTIONAL_METRICS_CONFIG[key as keyof typeof EMOTIONAL_METRICS_CONFIG];
        totalMetrics += metrics.length;
      }
      
      if (totalMetrics < 20) {
        throw new Error(`Attendu au moins 20 métriques, trouvé ${totalMetrics}`);
      }
      
      return `${totalMetrics} métriques configurées correctement`;
    });
  }

  /**
   * Test du connecteur émotionnel
   */
  private async testEmotionalConnector(): Promise<void> {
    console.log('🔗 Tests du Connecteur Émotionnel...');

    await this.runTest('Initialisation du Connecteur', async () => {
      this.connector = new EmotionalAgentConnector();
      if (!this.connector) {
        throw new Error('Échec de l\'initialisation du connecteur');
      }
      return 'Connecteur initialisé avec succès';
    });

    await this.runTest('Statut des Connexions', async () => {
      if (!this.connector) {
        throw new Error('Connecteur non initialisé');
      }
      
      const status = this.connector.getConnectionStatus();
      const agentCount = Object.keys(status).length;
      
      if (agentCount !== 5) {
        throw new Error(`Attendu 5 agents, trouvé ${agentCount}`);
      }
      
      return `${agentCount} agents configurés dans le connecteur`;
    });

    await this.runTest('Test de Diffusion de Message', async () => {
      if (!this.connector) {
        throw new Error('Connecteur non initialisé');
      }
      
      const testMessage = {
        type: 'TEST_MESSAGE',
        data: { test: true, timestamp: Date.now() }
      };
      
      // Note: En mode test, les agents ne sont pas réellement connectés
      // donc broadcastToAgents retournera 0, ce qui est attendu
      const result = await this.connector.broadcastToAgents(testMessage);
      
      return `Message de test diffusé (${result} agents connectés en mode test)`;
    });
  }

  /**
   * Test de l'interface personnalité
   */
  private async testPersonalityInterface(): Promise<void> {
    console.log('🎭 Tests de l\'Interface Personnalité...');

    await this.runTest('Configuration des Traits de Personnalité', async () => {
      const config = EMOTIONAL_ORGANS_CONFIG.personality;
      const traits = config.defaultTraits;
      
      // Vérifier les Big Five
      const bigFive = ['openness', 'conscientiousness', 'extraversion', 'agreeableness', 'neuroticism'];
      for (const trait of bigFive) {
        if (!(trait in traits)) {
          throw new Error(`Trait Big Five manquant: ${trait}`);
        }
      }
      
      // Vérifier les traits spéciaux Hanuman
      const specialTraits = ['divineWisdom', 'heroicCourage', 'devotion', 'adaptability', 'learning'];
      for (const trait of specialTraits) {
        if (!(trait in traits)) {
          throw new Error(`Trait spécial Hanuman manquant: ${trait}`);
        }
      }
      
      return `${Object.keys(traits).length} traits de personnalité configurés`;
    });

    await this.runTest('Capacités de l\'Interface Personnalité', async () => {
      const config = EMOTIONAL_ORGANS_CONFIG.personality;
      const requiredCapabilities = [
        'trait_adaptation',
        'contextual_behavior', 
        'personality_evolution',
        'behavioral_consistency',
        'preference_learning'
      ];
      
      for (const capability of requiredCapabilities) {
        if (!config.capabilities.includes(capability)) {
          throw new Error(`Capacité manquante: ${capability}`);
        }
      }
      
      return `${config.capabilities.length} capacités validées`;
    });
  }

  /**
   * Test de l'interface émotions
   */
  private async testEmotionsInterface(): Promise<void> {
    console.log('💭 Tests de l\'Interface Émotions...');

    await this.runTest('Émotions Supportées', async () => {
      const config = EMOTIONAL_ORGANS_CONFIG.emotions;
      const requiredEmotions = ['joy', 'sadness', 'anger', 'fear', 'surprise', 'disgust', 'neutral'];
      
      for (const emotion of requiredEmotions) {
        if (!config.supportedEmotions.includes(emotion)) {
          throw new Error(`Émotion manquante: ${emotion}`);
        }
      }
      
      return `${config.supportedEmotions.length} émotions supportées`;
    });

    await this.runTest('Techniques de Régulation', async () => {
      const config = EMOTIONAL_ORGANS_CONFIG.emotions;
      const requiredTechniques = ['cosmic_breathing', 'cycle_meditation', 'energy_harmonization'];
      
      for (const technique of requiredTechniques) {
        if (!config.regulationTechniques.includes(technique)) {
          throw new Error(`Technique de régulation manquante: ${technique}`);
        }
      }
      
      return `${config.regulationTechniques.length} techniques de régulation`;
    });
  }

  /**
   * Test de l'interface empathie
   */
  private async testEmpathyInterface(): Promise<void> {
    console.log('💝 Tests de l\'Interface Empathie...');

    await this.runTest('Méthodes de Détection d\'Émotions', async () => {
      const config = EMOTIONAL_ORGANS_CONFIG.empathy;
      const requiredMethods = ['nlp_analysis', 'behavioral_patterns', 'interaction_history', 'contextual_clues'];
      
      for (const method of requiredMethods) {
        if (!config.detectionMethods.includes(method)) {
          throw new Error(`Méthode de détection manquante: ${method}`);
        }
      }
      
      return `${config.detectionMethods.length} méthodes de détection`;
    });

    await this.runTest('Types de Réponses Empathiques', async () => {
      const config = EMOTIONAL_ORGANS_CONFIG.empathy;
      const requiredTypes = ['supportive', 'encouraging', 'calming', 'celebratory', 'understanding'];
      
      for (const type of requiredTypes) {
        if (!config.responseTypes.includes(type)) {
          throw new Error(`Type de réponse manquant: ${type}`);
        }
      }
      
      return `${config.responseTypes.length} types de réponses empathiques`;
    });
  }

  /**
   * Test de l'interface sociale
   */
  private async testSocialInterface(): Promise<void> {
    console.log('👥 Tests de l\'Interface Sociale...');

    await this.runTest('Types de Relations', async () => {
      const config = EMOTIONAL_ORGANS_CONFIG.social;
      const requiredTypes = ['new', 'regular', 'vip', 'friend', 'collaborator'];
      
      for (const type of requiredTypes) {
        if (!config.relationshipTypes.includes(type)) {
          throw new Error(`Type de relation manquant: ${type}`);
        }
      }
      
      return `${config.relationshipTypes.length} types de relations`;
    });

    await this.runTest('Types d\'Interactions', async () => {
      const config = EMOTIONAL_ORGANS_CONFIG.social;
      const requiredTypes = ['question', 'conversation', 'support', 'feedback', 'collaboration'];
      
      for (const type of requiredTypes) {
        if (!config.interactionTypes.includes(type)) {
          throw new Error(`Type d'interaction manquant: ${type}`);
        }
      }
      
      return `${config.interactionTypes.length} types d'interactions`;
    });
  }

  /**
   * Tests d'intégration
   */
  private async testIntegration(): Promise<void> {
    console.log('🔄 Tests d\'Intégration...');

    await this.runTest('Connexions Inter-Organes', async () => {
      const organs = Object.values(EMOTIONAL_ORGANS_CONFIG);
      let totalConnections = 0;
      
      for (const organ of organs) {
        totalConnections += organ.connections.length;
      }
      
      if (totalConnections < 10) {
        throw new Error(`Attendu au moins 10 connexions, trouvé ${totalConnections}`);
      }
      
      return `${totalConnections} connexions inter-organes configurées`;
    });

    await this.runTest('Utilitaires Hanuman', async () => {
      const consciousnessState = HanumanUtils.getConsciousnessState();
      
      if (!consciousnessState.awarenessLevel || consciousnessState.awarenessLevel < 0) {
        throw new Error('État de conscience invalide');
      }
      
      if (!consciousnessState.focusedOrgans || consciousnessState.focusedOrgans.length === 0) {
        throw new Error('Aucun organe focalisé');
      }
      
      return `État de conscience: ${consciousnessState.awarenessLevel}% d'éveil`;
    });
  }

  /**
   * Tests de performance
   */
  private async testPerformance(): Promise<void> {
    console.log('⚡ Tests de Performance...');

    await this.runTest('Temps de Réponse des Utilitaires', async () => {
      const startTime = Date.now();
      
      // Test multiple d'obtention de l'état de conscience
      for (let i = 0; i < 100; i++) {
        HanumanUtils.getConsciousnessState();
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      if (duration > 1000) {
        throw new Error(`Performance dégradée: ${duration}ms pour 100 appels`);
      }
      
      return `100 appels en ${duration}ms (${(duration/100).toFixed(2)}ms/appel)`;
    });

    await this.runTest('Validation de Santé des Organes', async () => {
      const organs = Object.keys(EMOTIONAL_ORGANS_CONFIG);
      let validOrgans = 0;
      
      for (const organKey of organs) {
        const organ = EMOTIONAL_ORGANS_CONFIG[organKey as keyof typeof EMOTIONAL_ORGANS_CONFIG];
        if (HanumanUtils.validateOrganHealth(organ.id)) {
          validOrgans++;
        }
      }
      
      if (validOrgans !== organs.length) {
        throw new Error(`${organs.length - validOrgans} organes en mauvaise santé`);
      }
      
      return `${validOrgans}/${organs.length} organes en bonne santé`;
    });
  }

  /**
   * Exécute un test individuel
   */
  private async runTest(testName: string, testFunction: () => Promise<string>): Promise<void> {
    const startTime = Date.now();
    
    try {
      const details = await testFunction();
      const duration = Date.now() - startTime;
      
      this.results.push({
        testName,
        status: 'PASS',
        duration,
        details
      });
      
      console.log(`  ✅ ${testName} (${duration}ms) - ${details}`);
      
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.results.push({
        testName,
        status: 'FAIL',
        duration,
        error: error as Error
      });
      
      console.log(`  ❌ ${testName} (${duration}ms) - ${(error as Error).message}`);
    }
  }

  /**
   * Nettoyage après les tests
   */
  private async cleanup(): Promise<void> {
    console.log('\n🧹 Nettoyage...');
    
    if (this.connector) {
      await this.connector.disconnect();
      this.connector = null;
    }
    
    console.log('✅ Nettoyage terminé');
  }

  /**
   * Génère le rapport final
   */
  private generateReport(): void {
    console.log('\n📊 ===== RAPPORT DE TESTS ===== 📊');
    
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const total = this.results.length;
    const successRate = ((passed / total) * 100).toFixed(1);
    
    console.log(`Tests Exécutés: ${total}`);
    console.log(`Réussis: ${passed} ✅`);
    console.log(`Échoués: ${failed} ❌`);
    console.log(`Taux de Réussite: ${successRate}%`);
    
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);
    console.log(`Durée Totale: ${totalDuration}ms`);
    
    if (failed > 0) {
      console.log('\n❌ Tests Échoués:');
      this.results
        .filter(r => r.status === 'FAIL')
        .forEach(r => {
          console.log(`  - ${r.testName}: ${r.error?.message}`);
        });
    }
    
    console.log('\n🎯 Résumé Sprint 4:');
    console.log('✅ Interface Personnalité d\'Hanuman');
    console.log('✅ Interface Émotions d\'Hanuman');
    console.log('✅ Interface Empathie d\'Hanuman');
    console.log('✅ Interface Relations Sociales d\'Hanuman');
    console.log('✅ Connecteur Agents Émotionnels');
    console.log('✅ Configuration et Intégration');
    
    console.log(`\n🌟 Sprint 4 - Personnalité et Émotions: ${successRate}% COMPLÉTÉ 🌟`);
    console.log('=======================================\n');
  }
}

// Exécution des tests si le script est lancé directement
if (require.main === module) {
  const testSuite = new EmotionalInterfacesTestSuite();
  testSuite.runAllTests().catch(console.error);
}

export default EmotionalInterfacesTestSuite;
