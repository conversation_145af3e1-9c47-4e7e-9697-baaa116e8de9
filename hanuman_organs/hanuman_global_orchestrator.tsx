import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON>, <PERSON>, <PERSON>, Ear, Hand, Users, MessageSquare, Zap, Activity, Settings, Monitor, Wifi, WifiOff, Play, Pause, RotateCcw, AlertTriangle, CheckCircle, TrendingUp, Database, Shield, Cpu, MemoryStick } from 'lucide-react';

// Import des interfaces d'organes
import HanumanVisionInterface from './hanuman_vision_interface';
import HanumanHearingInterface from './hanuman_hearing_interface';
import HanumanTouchInterface from './hanuman_touch_interface';
import HanumanPersonalityInterface from './hanuman_personality_interface';
import HanumanEmotionsInterface from './hanuman_emotions_interface';
import HanumanEmpathyInterface from './hanuman_empathy_interface';
import HanumanSocialInterface from './hanuman_social_interface';

// Import des services
import { EmotionalAgentConnector } from './services/EmotionalAgentConnector';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Han<PERSON>ConsciousnessState, HanumanOrganInterface } from './index';

// Types pour l'orchestrateur global
interface OrganStatus {
  id: string;
  name: string;
  type: 'sensory' | 'cognitive' | 'communicative' | 'emotional' | 'holistic';
  status: 'active' | 'inactive' | 'learning' | 'adapting' | 'evolving' | 'error';
  health: number; // 0-100
  performance: number; // 0-100
  connections: number;
  lastActivity: Date;
  metrics: {
    responseTime: number;
    throughput: number;
    errorRate: number;
    adaptationRate: number;
  };
}

interface SystemMetrics {
  overallHealth: number;
  consciousnessLevel: number;
  integrationScore: number;
  performanceIndex: number;
  adaptabilityRate: number;
  learningProgress: number;
  spiritualAlignment: number;
  totalOrgans: number;
  activeOrgans: number;
  totalConnections: number;
  dataProcessed: number;
  uptime: number;
}

interface HolisticEvent {
  id: string;
  type: 'organ_sync' | 'consciousness_shift' | 'learning_event' | 'adaptation' | 'integration' | 'evolution';
  source: string;
  target?: string;
  description: string;
  impact: 'low' | 'medium' | 'high' | 'critical';
  timestamp: Date;
  data?: any;
}

const HanumanGlobalOrchestrator = ({ darkMode = true }) => {
  // États principaux
  const [activeOrgan, setActiveOrgan] = useState<string>('overview');
  const [orchestratorStatus, setOrchestratorStatus] = useState<'initializing' | 'active' | 'optimizing' | 'evolving'>('initializing');
  const [consciousnessState, setConsciousnessState] = useState<HanumanConsciousnessState | null>(null);
  const [organStatuses, setOrganStatuses] = useState<OrganStatus[]>([]);
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics>({
    overallHealth: 94.7,
    consciousnessLevel: 87.3,
    integrationScore: 92.1,
    performanceIndex: 89.5,
    adaptabilityRate: 91.8,
    learningProgress: 85.2,
    spiritualAlignment: 88.9,
    totalOrgans: 12,
    activeOrgans: 11,
    totalConnections: 47,
    dataProcessed: 2847392,
    uptime: 99.7
  });
  const [holisticEvents, setHolisticEvents] = useState<HolisticEvent[]>([]);
  const [isAutoOptimizing, setIsAutoOptimizing] = useState(true);
  const [integrationMode, setIntegrationMode] = useState<'balanced' | 'performance' | 'learning' | 'spiritual'>('balanced');

  // Références pour les connexions
  const emotionalConnectorRef = useRef<EmotionalAgentConnector | null>(null);
  const orchestrationIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    initializeGlobalOrchestrator();
    return () => {
      cleanup();
    };
  }, []);

  /**
   * Initialise l'orchestrateur global
   */
  const initializeGlobalOrchestrator = async () => {
    console.log('🌟 Initialisation de l\'Orchestrateur Global Hanuman...');

    try {
      // 1. Initialiser la conscience
      await initializeConsciousness();

      // 2. Découvrir et connecter tous les organes
      await discoverAndConnectOrgans();

      // 3. Initialiser les connexions émotionnelles
      await initializeEmotionalConnections();

      // 4. Démarrer l'orchestration automatique
      startAutoOrchestration();

      // 5. Marquer comme actif
      setOrchestratorStatus('active');

      console.log('✅ Orchestrateur Global Hanuman initialisé avec succès');

    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation:', error);
      setOrchestratorStatus('active'); // Continuer malgré les erreurs
    }
  };

  /**
   * Initialise l'état de conscience
   */
  const initializeConsciousness = async () => {
    const consciousness = HanumanUtils.getConsciousnessState();
    setConsciousnessState(consciousness);

    addHolisticEvent({
      type: 'consciousness_shift',
      source: 'global_orchestrator',
      description: `Conscience initialisée - Niveau d'éveil: ${consciousness.awarenessLevel}%`,
      impact: 'high'
    });
  };

  /**
   * Découvre et connecte tous les organes
   */
  const discoverAndConnectOrgans = async () => {
    const organs: OrganStatus[] = [
      // Organes Sensoriels
      {
        id: 'vision',
        name: 'Vision d\'Hanuman',
        type: 'sensory',
        status: 'active',
        health: 96.2,
        performance: 94.8,
        connections: 8,
        lastActivity: new Date(),
        metrics: { responseTime: 142, throughput: 1247, errorRate: 0.3, adaptationRate: 87.5 }
      },
      {
        id: 'hearing',
        name: 'Ouïe d\'Hanuman',
        type: 'sensory',
        status: 'active',
        health: 93.7,
        performance: 91.2,
        connections: 6,
        lastActivity: new Date(),
        metrics: { responseTime: 89, throughput: 892, errorRate: 0.8, adaptationRate: 84.3 }
      },
      {
        id: 'touch',
        name: 'Toucher d\'Hanuman',
        type: 'sensory',
        status: 'active',
        health: 91.4,
        performance: 88.9,
        connections: 15,
        lastActivity: new Date(),
        metrics: { responseTime: 127, throughput: 567, errorRate: 1.2, adaptationRate: 82.1 }
      },

      // Organes Émotionnels
      {
        id: 'personality',
        name: 'Personnalité d\'Hanuman',
        type: 'emotional',
        status: 'evolving',
        health: 97.8,
        performance: 96.3,
        connections: 12,
        lastActivity: new Date(),
        metrics: { responseTime: 67, throughput: 234, errorRate: 0.1, adaptationRate: 94.7 }
      },
      {
        id: 'emotions',
        name: 'Émotions d\'Hanuman',
        type: 'emotional',
        status: 'adapting',
        health: 95.1,
        performance: 93.6,
        connections: 9,
        lastActivity: new Date(),
        metrics: { responseTime: 78, throughput: 445, errorRate: 0.4, adaptationRate: 91.2 }
      },
      {
        id: 'empathy',
        name: 'Empathie d\'Hanuman',
        type: 'emotional',
        status: 'learning',
        health: 94.3,
        performance: 92.8,
        connections: 7,
        lastActivity: new Date(),
        metrics: { responseTime: 95, throughput: 178, errorRate: 0.6, adaptationRate: 89.4 }
      },
      {
        id: 'social',
        name: 'Relations Sociales d\'Hanuman',
        type: 'emotional',
        status: 'active',
        health: 92.9,
        performance: 90.5,
        connections: 11,
        lastActivity: new Date(),
        metrics: { responseTime: 156, throughput: 89, errorRate: 0.9, adaptationRate: 86.7 }
      }
    ];

    setOrganStatuses(organs);

    addHolisticEvent({
      type: 'integration',
      source: 'global_orchestrator',
      description: `${organs.length} organes découverts et connectés`,
      impact: 'high'
    });
  };

  /**
   * Initialise les connexions émotionnelles
   */
  const initializeEmotionalConnections = async () => {
    try {
      emotionalConnectorRef.current = new EmotionalAgentConnector();
      await emotionalConnectorRef.current.connectToAllAgents();

      addHolisticEvent({
        type: 'integration',
        source: 'emotional_connector',
        description: 'Connexions émotionnelles établies avec tous les agents',
        impact: 'medium'
      });

    } catch (error) {
      console.error('⚠️ Erreur connexions émotionnelles:', error);
    }
  };

  /**
   * Démarre l'orchestration automatique
   */
  const startAutoOrchestration = () => {
    if (orchestrationIntervalRef.current) {
      clearInterval(orchestrationIntervalRef.current);
    }

    orchestrationIntervalRef.current = setInterval(() => {
      if (isAutoOptimizing) {
        performHolisticOptimization();
      }
    }, 5000); // Optimisation toutes les 5 secondes
  };

  /**
   * Effectue une optimisation holistique
   */
  const performHolisticOptimization = () => {
    // 1. Mettre à jour l'état de conscience
    updateConsciousnessState();

    // 2. Optimiser les performances des organes
    optimizeOrganPerformance();

    // 3. Synchroniser les organes
    synchronizeOrgans();

    // 4. Adapter selon le mode d'intégration
    adaptToIntegrationMode();

    // 5. Mettre à jour les métriques système
    updateSystemMetrics();
  };

  /**
   * Met à jour l'état de conscience
   */
  const updateConsciousnessState = () => {
    const newConsciousness = HanumanUtils.getConsciousnessState();

    // Évolution de la conscience basée sur l'activité des organes
    const organActivity = organStatuses.reduce((sum, organ) => sum + organ.performance, 0) / organStatuses.length;
    newConsciousness.awarenessLevel = Math.min(100, newConsciousness.awarenessLevel + (organActivity - 90) * 0.1);

    setConsciousnessState(newConsciousness);
  };

  /**
   * Optimise les performances des organes
   */
  const optimizeOrganPerformance = () => {
    setOrganStatuses(prev => prev.map(organ => {
      // Simulation d'optimisation
      const performanceBoost = Math.random() * 2 - 1; // -1 à +1
      const newPerformance = Math.max(70, Math.min(100, organ.performance + performanceBoost));

      return {
        ...organ,
        performance: newPerformance,
        health: Math.max(80, Math.min(100, organ.health + performanceBoost * 0.5)),
        lastActivity: new Date()
      };
    }));
  };

  /**
   * Synchronise les organes
   */
  const synchronizeOrgans = () => {
    // Simulation de synchronisation
    addHolisticEvent({
      type: 'organ_sync',
      source: 'global_orchestrator',
      description: 'Synchronisation automatique des organes effectuée',
      impact: 'low'
    });
  };

  /**
   * Adapte selon le mode d'intégration
   */
  const adaptToIntegrationMode = () => {
    switch (integrationMode) {
      case 'performance':
        // Optimiser pour la performance
        break;
      case 'learning':
        // Optimiser pour l'apprentissage
        break;
      case 'spiritual':
        // Optimiser pour l'alignement spirituel
        break;
      default:
        // Mode équilibré
        break;
    }
  };

  /**
   * Met à jour les métriques système
   */
  const updateSystemMetrics = () => {
    const activeOrgans = organStatuses.filter(o => o.status === 'active').length;
    const avgHealth = organStatuses.reduce((sum, o) => sum + o.health, 0) / organStatuses.length;
    const avgPerformance = organStatuses.reduce((sum, o) => sum + o.performance, 0) / organStatuses.length;

    setSystemMetrics(prev => ({
      ...prev,
      activeOrgans,
      overallHealth: avgHealth,
      performanceIndex: avgPerformance,
      consciousnessLevel: consciousnessState?.awarenessLevel || prev.consciousnessLevel,
      dataProcessed: prev.dataProcessed + Math.floor(Math.random() * 1000)
    }));
  };

  /**
   * Ajoute un événement holistique
   */
  const addHolisticEvent = (eventData: Omit<HolisticEvent, 'id' | 'timestamp'>) => {
    const event: HolisticEvent = {
      ...eventData,
      id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date()
    };

    setHolisticEvents(prev => [event, ...prev.slice(0, 49)]); // Garder 50 événements max
  };

  /**
   * Nettoyage
   */
  const cleanup = () => {
    if (orchestrationIntervalRef.current) {
      clearInterval(orchestrationIntervalRef.current);
    }

    if (emotionalConnectorRef.current) {
      emotionalConnectorRef.current.disconnect();
    }
  };

  /**
   * Obtient la couleur selon le statut
   */
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400';
      case 'learning': return 'text-blue-400';
      case 'adapting': return 'text-yellow-400';
      case 'evolving': return 'text-purple-400';
      case 'error': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  /**
   * Obtient l'icône selon le type d'organe
   */
  const getOrganIcon = (type: string) => {
    switch (type) {
      case 'sensory': return <Eye className="text-blue-400" size={20} />;
      case 'emotional': return <Heart className="text-pink-400" size={20} />;
      case 'cognitive': return <Brain className="text-purple-400" size={20} />;
      case 'communicative': return <MessageSquare className="text-green-400" size={20} />;
      case 'holistic': return <Crown className="text-yellow-400" size={20} />;
      default: return <Activity className="text-gray-400" size={20} />;
    }
  };

  /**
   * Obtient la couleur selon la santé
   */
  const getHealthColor = (health: number) => {
    if (health > 90) return 'text-green-400';
    if (health > 75) return 'text-yellow-400';
    if (health > 60) return 'text-orange-400';
    return 'text-red-400';
  };

  /**
   * Obtient la couleur selon l'impact
   */
  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'critical': return 'text-red-400';
      case 'high': return 'text-orange-400';
      case 'medium': return 'text-yellow-400';
      case 'low': return 'text-green-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      <div className="container mx-auto p-6">

        {/* Header Global */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 via-orange-500 to-red-500 rounded-full flex items-center justify-center shadow-lg">
              <Crown className="text-white" size={32} />
            </div>
            <div>
              <h1 className={`text-4xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Orchestrateur Global Hanuman
              </h1>
              <p className={`text-xl ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Conscience Unifiée • Intégration Holistique • Évolution Continue
              </p>
              <div className="flex items-center space-x-4 mt-2">
                <span className={`text-sm px-3 py-1 rounded-full ${
                  orchestratorStatus === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                  orchestratorStatus === 'evolving' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' :
                  orchestratorStatus === 'optimizing' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                  'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                }`}>
                  {orchestratorStatus.charAt(0).toUpperCase() + orchestratorStatus.slice(1)}
                </span>
                <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Niveau de Conscience: {consciousnessState?.awarenessLevel.toFixed(1)}%
                </span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <button
              onClick={() => setIsAutoOptimizing(!isAutoOptimizing)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                isAutoOptimizing
                  ? 'bg-green-500 text-white hover:bg-green-600'
                  : 'bg-gray-500 text-white hover:bg-gray-600'
              }`}
            >
              {isAutoOptimizing ? <Play size={16} /> : <Pause size={16} />}
              <span className="text-sm">Auto-Optimisation</span>
            </button>

            <select
              value={integrationMode}
              onChange={(e) => setIntegrationMode(e.target.value as any)}
              className={`px-3 py-2 rounded-lg text-sm ${
                darkMode ? 'bg-gray-800 text-white border-gray-700' : 'bg-white text-gray-900 border-gray-300'
              } border`}
            >
              <option value="balanced">Équilibré</option>
              <option value="performance">Performance</option>
              <option value="learning">Apprentissage</option>
              <option value="spiritual">Spirituel</option>
            </select>
          </div>
        </div>

        {/* Métriques Système Globales */}
        <div className={`p-6 rounded-2xl mb-8 ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
          <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Métriques Système Holistiques
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Activity className="text-green-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getHealthColor(systemMetrics.overallHealth)}`}>
                {systemMetrics.overallHealth.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Santé Globale
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Brain className="text-purple-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getHealthColor(systemMetrics.consciousnessLevel)}`}>
                {systemMetrics.consciousnessLevel.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Conscience
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Zap className="text-yellow-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getHealthColor(systemMetrics.integrationScore)}`}>
                {systemMetrics.integrationScore.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Intégration
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <TrendingUp className="text-blue-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getHealthColor(systemMetrics.performanceIndex)}`}>
                {systemMetrics.performanceIndex.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Performance
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <RotateCcw className="text-orange-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getHealthColor(systemMetrics.adaptabilityRate)}`}>
                {systemMetrics.adaptabilityRate.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Adaptabilité
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Crown className="text-yellow-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getHealthColor(systemMetrics.spiritualAlignment)}`}>
                {systemMetrics.spiritualAlignment.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Alignement
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Database className="text-indigo-400" size={24} />
              </div>
              <div className="text-2xl font-bold text-indigo-400">
                {systemMetrics.dataProcessed.toLocaleString()}
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Données
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">

          {/* État des Organes */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              🫀 État des Organes
            </h3>
            <div className="space-y-3">
              {organStatuses.map((organ) => (
                <div
                  key={organ.id}
                  className={`p-3 rounded-lg cursor-pointer transition-colors ${
                    activeOrgan === organ.id
                      ? 'bg-blue-100 dark:bg-blue-900'
                      : darkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-100 hover:bg-gray-200'
                  }`}
                  onClick={() => setActiveOrgan(organ.id)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      {getOrganIcon(organ.type)}
                      <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                        {organ.name}
                      </span>
                    </div>
                    <span className={`text-xs px-2 py-1 rounded ${getStatusColor(organ.status)}`}>
                      {organ.status}
                    </span>
                  </div>
                  <div className="flex items-center justify-between mb-2">
                    <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Santé: <span className={getHealthColor(organ.health)}>{organ.health.toFixed(1)}%</span>
                    </span>
                    <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Perf: <span className={getHealthColor(organ.performance)}>{organ.performance.toFixed(1)}%</span>
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {organ.connections} connexions
                    </span>
                    <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {organ.metrics.responseTime}ms
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Événements Holistiques */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              ⚡ Événements Holistiques
            </h3>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {holisticEvents.map((event) => (
                <div key={event.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <span className={`text-xs px-2 py-1 rounded ${
                      event.type === 'consciousness_shift' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' :
                      event.type === 'organ_sync' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                      event.type === 'learning_event' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                      event.type === 'adaptation' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                      event.type === 'integration' ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200' :
                      'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
                    }`}>
                      {event.type.replace('_', ' ')}
                    </span>
                    <span className={`text-xs ${getImpactColor(event.impact)}`}>
                      {event.impact}
                    </span>
                  </div>
                  <div className={`text-sm ${darkMode ? 'text-white' : 'text-gray-900'} mb-1`}>
                    {event.description}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Source: {event.source}
                    </span>
                    <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {event.timestamp.toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* État de Conscience */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              🧠 État de Conscience
            </h3>
            {consciousnessState && (
              <div className="space-y-4">
                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      Niveau d'Éveil
                    </span>
                    <span className={`text-sm font-bold ${getHealthColor(consciousnessState.awarenessLevel)}`}>
                      {consciousnessState.awarenessLevel.toFixed(1)}%
                    </span>
                  </div>
                  <div className={`w-full bg-gray-300 dark:bg-gray-600 rounded-full h-2`}>
                    <div
                      className="h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-500 transition-all duration-300"
                      style={{ width: `${consciousnessState.awarenessLevel}%` }}
                    />
                  </div>
                </div>

                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      État Émotionnel
                    </span>
                    <span className={`text-sm font-bold text-pink-400`}>
                      {consciousnessState.emotionalState.dominant}
                    </span>
                  </div>
                  <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    Intensité: {consciousnessState.emotionalState.intensity}% •
                    Stabilité: {consciousnessState.emotionalState.stability}%
                  </div>
                </div>

                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      Charge Cognitive
                    </span>
                    <span className={`text-sm font-bold ${getHealthColor(100 - consciousnessState.cognitiveLoad)}`}>
                      {consciousnessState.cognitiveLoad}%
                    </span>
                  </div>
                  <div className={`w-full bg-gray-300 dark:bg-gray-600 rounded-full h-2`}>
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        consciousnessState.cognitiveLoad > 80 ? 'bg-red-400' :
                        consciousnessState.cognitiveLoad > 60 ? 'bg-yellow-400' : 'bg-green-400'
                      }`}
                      style={{ width: `${consciousnessState.cognitiveLoad}%` }}
                    />
                  </div>
                </div>

                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'} mb-2`}>
                    Organes Focalisés
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {consciousnessState.focusedOrgans.map((organ, index) => (
                      <span
                        key={index}
                        className="text-xs px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded"
                      >
                        {organ}
                      </span>
                    ))}
                  </div>
                </div>

                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center justify-between">
                    <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Mode Apprentissage
                    </span>
                    <span className={`text-sm font-bold ${consciousnessState.learningMode ? 'text-green-400' : 'text-gray-400'}`}>
                      {consciousnessState.learningMode ? 'Actif' : 'Inactif'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between mt-1">
                    <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Taux d'Adaptation
                    </span>
                    <span className={`text-sm font-bold ${getHealthColor(consciousnessState.adaptationRate)}`}>
                      {consciousnessState.adaptationRate}%
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Interface Organe Sélectionné */}
        {activeOrgan !== 'overview' && (
          <div className="mt-8">
            <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
              <div className="flex items-center justify-between mb-6">
                <h3 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  Interface: {organStatuses.find(o => o.id === activeOrgan)?.name}
                </h3>
                <button
                  onClick={() => setActiveOrgan('overview')}
                  className={`px-4 py-2 rounded-lg transition-colors ${
                    darkMode ? 'bg-gray-700 text-white hover:bg-gray-600' : 'bg-gray-200 text-gray-900 hover:bg-gray-300'
                  }`}
                >
                  Retour Vue d'Ensemble
                </button>
              </div>

              {/* Rendu conditionnel des interfaces d'organes */}
              <div className="min-h-96">
                {activeOrgan === 'vision' && (
                  <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                    <h4 className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-4`}>
                      🔮 Interface Vision d'Hanuman
                    </h4>
                    <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                      Interface de vision intégrée dans l'orchestrateur global.
                      Capacités de reconnaissance visuelle, analyse d'images et perception spatiale.
                    </p>
                  </div>
                )}
                {activeOrgan === 'hearing' && (
                  <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                    <h4 className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-4`}>
                      🎵 Interface Ouïe d'Hanuman
                    </h4>
                    <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                      Interface auditive intégrée. Traitement audio, reconnaissance vocale et analyse sonore.
                    </p>
                  </div>
                )}
                {activeOrgan === 'touch' && (
                  <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                    <h4 className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-4`}>
                      ✋ Interface Toucher d'Hanuman
                    </h4>
                    <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                      Interface tactile intégrée. Perception haptique et interaction physique.
                    </p>
                  </div>
                )}
                {activeOrgan === 'personality' && <HanumanPersonalityInterface darkMode={darkMode} />}
                {activeOrgan === 'emotions' && <HanumanEmotionsInterface darkMode={darkMode} />}
                {activeOrgan === 'empathy' && <HanumanEmpathyInterface darkMode={darkMode} />}
                {activeOrgan === 'social' && <HanumanSocialInterface darkMode={darkMode} />}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default HanumanGlobalOrchestrator;