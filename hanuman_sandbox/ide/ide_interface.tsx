import React, { useState, useEffect, useCallback } from 'react';
import { VSCodeServerManager, VSCodeInstance } from './vscode/vscode_server_manager';
import { RooCoderIntegration } from './vscode/roo_coder_integration';
import { TemplateManager } from './templates/template_manager';
import { TemplateInterface } from './templates/template_interface';
import { GitManager, GitRepository } from './git/git_manager';
import { EnvironmentSimulator, SimulationEnvironment } from './simulator/environment_simulator';
import { SandboxInfrastructure } from '../infrastructure/sandbox_infrastructure';

// Types pour l'interface IDE
export interface IDEInterfaceProps {
  infrastructure: SandboxInfrastructure;
  onError?: (error: Error) => void;
}

export interface IDEStats {
  vscodeInstances: number;
  activeRepositories: number;
  runningSimulations: number;
  templatesAvailable: number;
  totalProjects: number;
}

export interface ProjectInfo {
  id: string;
  name: string;
  type: string;
  agentId?: string;
  organId?: string;
  vscodeUrl?: string;
  repositoryId?: string;
  simulationId?: string;
  status: 'active' | 'inactive' | 'error';
  lastActivity: Date;
}

/**
 * Interface Principale de l'IDE Hanuman
 * Intègre VS Code, Roo Coder, Templates, Git et Simulation
 */
export const IDEInterface: React.FC<IDEInterfaceProps> = ({
  infrastructure,
  onError
}) => {
  const [vscodeManager] = useState(() => new VSCodeServerManager(infrastructure));
  const [rooCoderIntegration] = useState(() => new RooCoderIntegration());
  const [templateManager] = useState(() => new TemplateManager());
  const [gitManager] = useState(() => new GitManager());
  const [environmentSimulator] = useState(() => new EnvironmentSimulator());

  const [activeTab, setActiveTab] = useState<'overview' | 'vscode' | 'templates' | 'git' | 'simulator'>('overview');
  const [stats, setStats] = useState<IDEStats>({
    vscodeInstances: 0,
    activeRepositories: 0,
    runningSimulations: 0,
    templatesAvailable: 0,
    totalProjects: 0
  });
  const [projects, setProjects] = useState<ProjectInfo[]>([]);
  const [vscodeInstances, setVscodeInstances] = useState<VSCodeInstance[]>([]);
  const [repositories, setRepositories] = useState<GitRepository[]>([]);
  const [simulations, setSimulations] = useState<SimulationEnvironment[]>([]);

  useEffect(() => {
    setupEventHandlers();
    loadInitialData();

    return () => {
      cleanup();
    };
  }, []);

  const setupEventHandlers = () => {
    // VS Code Events
    vscodeManager.on('vscode:deployed', handleVSCodeDeployed);
    vscodeManager.on('vscode:stopped', handleVSCodeStopped);
    vscodeManager.on('vscode:error', handleVSCodeError);

    // Git Events
    gitManager.on('repository:initialized', handleRepositoryInitialized);
    gitManager.on('repository:committed', handleRepositoryCommitted);
    gitManager.on('repository:error', handleGitError);

    // Simulation Events
    environmentSimulator.on('environment:created', handleSimulationCreated);
    environmentSimulator.on('environment:stopped', handleSimulationStopped);
    environmentSimulator.on('environment:error', handleSimulationError);

    // Template Events
    templateManager.on('generation:completed', handleProjectGenerated);
    templateManager.on('generation:error', handleTemplateError);
  };

  const loadInitialData = async () => {
    try {
      // Charger les données existantes
      const vscodeList = vscodeManager.getInstances();
      const repoList = gitManager.getRepositories();
      const simList = environmentSimulator.getEnvironments();
      const templates = templateManager.getTemplates();

      setVscodeInstances(vscodeList);
      setRepositories(repoList);
      setSimulations(simList);

      // Construire la liste des projets
      const projectList = buildProjectList(vscodeList, repoList, simList);
      setProjects(projectList);

      // Mettre à jour les statistiques
      setStats({
        vscodeInstances: vscodeList.length,
        activeRepositories: repoList.filter(r => r.status.clean === false).length,
        runningSimulations: simList.filter(s => s.status === 'running').length,
        templatesAvailable: templates.length,
        totalProjects: projectList.length
      });

    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
      onError?.(error as Error);
    }
  };

  const buildProjectList = (
    vscodeList: VSCodeInstance[],
    repoList: GitRepository[],
    simList: SimulationEnvironment[]
  ): ProjectInfo[] => {
    const projectMap = new Map<string, ProjectInfo>();

    // Ajouter les instances VS Code
    vscodeList.forEach(instance => {
      const projectId = instance.agentId || instance.organId || instance.id;
      projectMap.set(projectId, {
        id: projectId,
        name: instance.agentId || instance.organId || 'Unknown Project',
        type: 'development',
        agentId: instance.agentId,
        organId: instance.organId,
        vscodeUrl: instance.url,
        status: instance.status === 'running' ? 'active' : 'inactive',
        lastActivity: instance.lastActivity
      });
    });

    // Ajouter les informations des dépôts
    repoList.forEach(repo => {
      const projectId = repo.agentId || repo.organId || repo.id;
      const existing = projectMap.get(projectId);
      if (existing) {
        existing.repositoryId = repo.id;
      } else {
        projectMap.set(projectId, {
          id: projectId,
          name: repo.name,
          type: 'repository',
          agentId: repo.agentId,
          organId: repo.organId,
          repositoryId: repo.id,
          status: 'active',
          lastActivity: repo.lastActivity
        });
      }
    });

    // Ajouter les informations des simulations
    simList.forEach(sim => {
      const projectId = sim.agentId || sim.organId || sim.id;
      const existing = projectMap.get(projectId);
      if (existing) {
        existing.simulationId = sim.id;
      } else {
        projectMap.set(projectId, {
          id: projectId,
          name: sim.name,
          type: 'simulation',
          agentId: sim.agentId,
          organId: sim.organId,
          simulationId: sim.id,
          status: sim.status === 'running' ? 'active' : 'inactive',
          lastActivity: sim.lastActivity
        });
      }
    });

    return Array.from(projectMap.values());
  };

  // Event Handlers
  const handleVSCodeDeployed = useCallback((instance: VSCodeInstance) => {
    setVscodeInstances(prev => [...prev, instance]);
    loadInitialData();
  }, []);

  const handleVSCodeStopped = useCallback((instance: VSCodeInstance) => {
    setVscodeInstances(prev => prev.filter(i => i.id !== instance.id));
    loadInitialData();
  }, []);

  const handleVSCodeError = useCallback((data: any) => {
    console.error('Erreur VS Code:', data.error);
    onError?.(data.error);
  }, [onError]);

  const handleRepositoryInitialized = useCallback((repo: GitRepository) => {
    setRepositories(prev => [...prev, repo]);
    loadInitialData();
  }, []);

  const handleRepositoryCommitted = useCallback((data: any) => {
    loadInitialData();
  }, []);

  const handleGitError = useCallback((data: any) => {
    console.error('Erreur Git:', data.error);
    onError?.(data.error);
  }, [onError]);

  const handleSimulationCreated = useCallback((simulation: SimulationEnvironment) => {
    setSimulations(prev => [...prev, simulation]);
    loadInitialData();
  }, []);

  const handleSimulationStopped = useCallback((simulation: SimulationEnvironment) => {
    setSimulations(prev => prev.filter(s => s.id !== simulation.id));
    loadInitialData();
  }, []);

  const handleSimulationError = useCallback((data: any) => {
    console.error('Erreur Simulation:', data.error);
    onError?.(data.error);
  }, [onError]);

  const handleProjectGenerated = useCallback((data: any) => {
    console.log('Projet généré:', data);
    loadInitialData();
  }, []);

  const handleTemplateError = useCallback((data: any) => {
    console.error('Erreur Template:', data.error);
    onError?.(data.error);
  }, [onError]);

  const cleanup = () => {
    vscodeManager.removeAllListeners();
    gitManager.cleanup();
    environmentSimulator.cleanup();
  };

  const createNewProject = async (agentId: string, organId?: string) => {
    try {
      // Créer un conteneur pour le projet
      const container = await infrastructure.createContainer({
        name: `${agentId}-development`,
        type: 'development',
        namespace: 'development',
        agentId,
        organId,
        securityLevel: 'medium'
      });

      // Déployer VS Code
      const vscodeInstance = await vscodeManager.deployVSCodeServer(container);

      // Configurer Roo Coder
      await rooCoderIntegration.configureForInstance(vscodeInstance);

      // Initialiser Git
      const repository = await gitManager.initializeRepository(vscodeInstance);

      // Créer l'environnement de simulation
      const simulation = await environmentSimulator.createEnvironment(container, vscodeInstance);

      console.log(`✅ Projet créé avec succès pour ${agentId}`);
      loadInitialData();

    } catch (error) {
      console.error('Erreur lors de la création du projet:', error);
      onError?.(error as Error);
    }
  };

  const openProject = (project: ProjectInfo) => {
    if (project.vscodeUrl) {
      window.open(project.vscodeUrl, '_blank');
    }
  };

  const getProjectStatusColor = (status: ProjectInfo['status']) => {
    const colors = {
      active: 'text-green-600 bg-green-100',
      inactive: 'text-gray-600 bg-gray-100',
      error: 'text-red-600 bg-red-100'
    };
    return colors[status];
  };

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <div className="flex items-center">
            <span className="text-2xl mr-3">💻</span>
            <div>
              <p className="text-sm font-medium text-blue-700">VS Code</p>
              <p className="text-2xl font-bold text-blue-900">{stats.vscodeInstances}</p>
            </div>
          </div>
        </div>

        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
          <div className="flex items-center">
            <span className="text-2xl mr-3">📁</span>
            <div>
              <p className="text-sm font-medium text-green-700">Dépôts Git</p>
              <p className="text-2xl font-bold text-green-900">{stats.activeRepositories}</p>
            </div>
          </div>
        </div>

        <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
          <div className="flex items-center">
            <span className="text-2xl mr-3">🎭</span>
            <div>
              <p className="text-sm font-medium text-purple-700">Simulations</p>
              <p className="text-2xl font-bold text-purple-900">{stats.runningSimulations}</p>
            </div>
          </div>
        </div>

        <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
          <div className="flex items-center">
            <span className="text-2xl mr-3">📋</span>
            <div>
              <p className="text-sm font-medium text-yellow-700">Templates</p>
              <p className="text-2xl font-bold text-yellow-900">{stats.templatesAvailable}</p>
            </div>
          </div>
        </div>

        <div className="bg-indigo-50 p-4 rounded-lg border border-indigo-200">
          <div className="flex items-center">
            <span className="text-2xl mr-3">🚀</span>
            <div>
              <p className="text-sm font-medium text-indigo-700">Projets</p>
              <p className="text-2xl font-bold text-indigo-900">{stats.totalProjects}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Actions rapides */}
      <div className="bg-white rounded-lg shadow border p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Actions Rapides</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={() => setActiveTab('templates')}
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left"
          >
            <div className="flex items-center mb-2">
              <span className="text-2xl mr-3">📋</span>
              <h4 className="font-medium">Nouveau Projet</h4>
            </div>
            <p className="text-sm text-gray-600">Créer un projet à partir d'un template</p>
          </button>

          <button
            onClick={() => setActiveTab('vscode')}
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left"
          >
            <div className="flex items-center mb-2">
              <span className="text-2xl mr-3">💻</span>
              <h4 className="font-medium">Ouvrir VS Code</h4>
            </div>
            <p className="text-sm text-gray-600">Accéder aux instances VS Code</p>
          </button>

          <button
            onClick={() => setActiveTab('simulator')}
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left"
          >
            <div className="flex items-center mb-2">
              <span className="text-2xl mr-3">🎭</span>
              <h4 className="font-medium">Simulateur</h4>
            </div>
            <p className="text-sm text-gray-600">Gérer les environnements de simulation</p>
          </button>
        </div>
      </div>

      {/* Liste des projets */}
      <div className="bg-white rounded-lg shadow border">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800">Projets Récents</h3>
        </div>
        <div className="p-6">
          {projects.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p className="text-lg">Aucun projet trouvé</p>
              <p className="text-sm">Créez votre premier projet avec un template</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {projects.map(project => (
                <div
                  key={project.id}
                  className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => openProject(project)}
                >
                  <div className="flex justify-between items-start mb-3">
                    <h4 className="font-medium text-gray-800">{project.name}</h4>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getProjectStatusColor(project.status)}`}>
                      {project.status}
                    </span>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-3">Type: {project.type}</p>
                  
                  <div className="flex items-center space-x-3 text-xs text-gray-500">
                    {project.vscodeUrl && <span>💻 VS Code</span>}
                    {project.repositoryId && <span>📁 Git</span>}
                    {project.simulationId && <span>🎭 Simulation</span>}
                  </div>
                  
                  <p className="text-xs text-gray-400 mt-2">
                    Dernière activité: {project.lastActivity.toLocaleDateString()}
                  </p>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const renderVSCodeTab = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow border p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Instances VS Code</h3>
        
        {vscodeInstances.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>Aucune instance VS Code active</p>
          </div>
        ) : (
          <div className="space-y-4">
            {vscodeInstances.map(instance => (
              <div key={instance.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="font-medium text-gray-800">
                      {instance.agentId || instance.organId || 'Instance VS Code'}
                    </h4>
                    <p className="text-sm text-gray-600">{instance.url}</p>
                    {instance.tunnelUrl && (
                      <p className="text-sm text-blue-600">Tunnel: {instance.tunnelUrl}</p>
                    )}
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => window.open(instance.url, '_blank')}
                      className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                    >
                      Ouvrir
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="bg-white rounded-lg shadow border">
      <div className="border-b border-gray-200">
        <div className="px-6 py-4">
          <h2 className="text-2xl font-bold text-gray-800">
            💻 IDE Hanuman - Environnement de Développement
          </h2>
          <p className="text-gray-600 mt-1">
            VS Code + Roo Coder + Templates + Git + Simulation intégrés
          </p>
        </div>

        {/* Navigation par onglets */}
        <div className="px-6">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'overview', name: 'Vue d\'ensemble', icon: '📊' },
              { id: 'vscode', name: 'VS Code', icon: '💻' },
              { id: 'templates', name: 'Templates', icon: '📋' },
              { id: 'git', name: 'Git', icon: '📁' },
              { id: 'simulator', name: 'Simulateur', icon: '🎭' }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.icon} {tab.name}
              </button>
            ))}
          </nav>
        </div>
      </div>

      <div className="p-6">
        {activeTab === 'overview' && renderOverview()}
        {activeTab === 'vscode' && renderVSCodeTab()}
        {activeTab === 'templates' && (
          <TemplateInterface 
            templateManager={templateManager}
            onProjectGenerated={(path, files) => {
              console.log(`Projet généré: ${path} (${files.length} fichiers)`);
              loadInitialData();
            }}
            onError={onError}
          />
        )}
        {activeTab === 'git' && (
          <div className="text-center py-12 text-gray-500">
            <p className="text-lg">Interface Git</p>
            <p className="text-sm">À implémenter dans les prochaines itérations</p>
          </div>
        )}
        {activeTab === 'simulator' && (
          <div className="text-center py-12 text-gray-500">
            <p className="text-lg">Interface Simulateur</p>
            <p className="text-sm">À implémenter dans les prochaines itérations</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default IDEInterface;
