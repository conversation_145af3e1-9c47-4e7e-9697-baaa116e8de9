import { EventEmitter } from 'events';
import { RooCoderTemplate } from '../vscode/roo_coder_integration';

// Types pour le gestionnaire de templates
export interface ProjectTemplate {
  id: string;
  name: string;
  description: string;
  category: 'agent' | 'organ' | 'interface' | 'service' | 'microservice' | 'full-project';
  version: string;
  author: string;
  tags: string[];
  structure: ProjectStructure;
  files: TemplateFile[];
  dependencies: string[];
  scripts: Record<string, string>;
  configuration: TemplateConfiguration;
  documentation: TemplateDocumentation;
}

export interface ProjectStructure {
  directories: string[];
  rootFiles: string[];
  srcStructure: Record<string, string[]>;
}

export interface TemplateFile {
  path: string;
  content: string;
  variables: string[];
  conditional?: string;
  executable?: boolean;
}

export interface TemplateConfiguration {
  environment: Record<string, string>;
  buildConfig: any;
  testConfig: any;
  lintConfig: any;
  typeScriptConfig: any;
}

export interface TemplateDocumentation {
  readme: string;
  setup: string[];
  usage: string[];
  examples: string[];
  troubleshooting: Record<string, string>;
}

export interface TemplateVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'select' | 'multiselect';
  description: string;
  required: boolean;
  defaultValue?: any;
  options?: string[];
  validation?: RegExp;
}

export interface TemplateGenerationOptions {
  templateId: string;
  projectName: string;
  outputPath: string;
  variables: Record<string, any>;
  overwrite: boolean;
  dryRun: boolean;
}

/**
 * Gestionnaire de Templates pour la Sandbox Hanuman
 * Gère la création, génération et personnalisation de templates de projets
 */
export class TemplateManager extends EventEmitter {
  private templates: Map<string, ProjectTemplate> = new Map();
  private rooCoderTemplates: Map<string, RooCoderTemplate> = new Map();

  constructor() {
    super();
    this.loadDefaultTemplates();
  }

  /**
   * Charge les templates par défaut pour Hanuman
   */
  private loadDefaultTemplates(): void {
    const defaultTemplates: ProjectTemplate[] = [
      {
        id: 'hanuman-agent-complete',
        name: 'Agent Hanuman Complet',
        description: 'Template complet pour créer un nouvel agent Hanuman avec toutes les fonctionnalités',
        category: 'agent',
        version: '1.0.0',
        author: 'Hanuman Team',
        tags: ['agent', 'typescript', 'react', 'tests'],
        structure: {
          directories: [
            'src',
            'src/components',
            'src/services',
            'src/types',
            'src/utils',
            'tests',
            'tests/unit',
            'tests/integration',
            'docs',
            'config'
          ],
          rootFiles: [
            'package.json',
            'tsconfig.json',
            'jest.config.js',
            'README.md',
            '.gitignore',
            '.eslintrc.js',
            'docker-compose.yml'
          ],
          srcStructure: {
            'components': ['AgentInterface.tsx', 'AgentDashboard.tsx', 'AgentControls.tsx'],
            'services': ['AgentService.ts', 'CommunicationService.ts', 'StateManager.ts'],
            'types': ['AgentTypes.ts', 'EventTypes.ts', 'ConfigTypes.ts'],
            'utils': ['helpers.ts', 'validators.ts', 'constants.ts']
          }
        },
        files: [
          {
            path: 'src/{{agentName}}.ts',
            content: `
import { EventEmitter } from 'events';
import { HanumanOrganOrchestrator } from '../services/HanumanOrganOrchestrator';
import { {{agentName}}Config, {{agentName}}State } from './types/AgentTypes';

/**
 * Agent {{agentName}} - {{description}}
 * Responsabilités: {{responsibilities}}
 * Capacités: {{capabilities}}
 */
export class {{agentName}} extends EventEmitter {
  private config: {{agentName}}Config;
  private state: {{agentName}}State;
  private orchestrator: HanumanOrganOrchestrator;

  constructor(orchestrator: HanumanOrganOrchestrator, config: {{agentName}}Config) {
    super();
    this.orchestrator = orchestrator;
    this.config = config;
    this.initializeAgent();
  }

  private async initializeAgent(): Promise<void> {
    this.state = {
      isActive: false,
      lastActivity: new Date(),
      capabilities: this.config.capabilities,
      performance: {
        tasksCompleted: 0,
        averageResponseTime: 0,
        successRate: 100
      }
    };

    await this.setupEventHandlers();
    await this.loadCapabilities();
    
    this.emit('agent:initialized', { agent: '{{agentName}}' });
    console.log('🤖 Agent {{agentName}} initialisé avec succès');
  }

  private async setupEventHandlers(): Promise<void> {
    this.orchestrator.on('neural:signal-received', this.handleNeuralSignal.bind(this));
    this.orchestrator.on('organ:request', this.handleOrganRequest.bind(this));
  }

  private async loadCapabilities(): Promise<void> {
    // Chargement des capacités spécifiques à l'agent
    {{#each capabilities}}
    await this.load{{pascalCase name}}Capability();
    {{/each}}
  }

  private async handleNeuralSignal(signal: any): Promise<void> {
    this.state.lastActivity = new Date();
    
    try {
      const result = await this.processSignal(signal);
      this.updatePerformanceMetrics(true);
      this.emit('agent:signal-processed', { agent: '{{agentName}}', signal, result });
    } catch (error) {
      this.updatePerformanceMetrics(false);
      this.emit('agent:error', { agent: '{{agentName}}', error });
    }
  }

  private async processSignal(signal: any): Promise<any> {
    // Traitement spécialisé du signal selon le type d'agent
    switch (signal.type) {
      {{#each signalTypes}}
      case '{{type}}':
        return await this.handle{{pascalCase type}}(signal);
      {{/each}}
      default:
        throw new Error(\`Type de signal non supporté: \${signal.type}\`);
    }
  }

  async activate(): Promise<void> {
    this.state.isActive = true;
    this.emit('agent:activated', { agent: '{{agentName}}' });
    console.log('✅ Agent {{agentName}} activé');
  }

  async deactivate(): Promise<void> {
    this.state.isActive = false;
    this.emit('agent:deactivated', { agent: '{{agentName}}' });
    console.log('😴 Agent {{agentName}} désactivé');
  }

  getState(): {{agentName}}State {
    return { ...this.state };
  }

  getPerformanceMetrics() {
    return {
      ...this.state.performance,
      uptime: Date.now() - this.state.lastActivity.getTime(),
      status: this.state.isActive ? 'active' : 'inactive'
    };
  }

  private updatePerformanceMetrics(success: boolean): void {
    this.state.performance.tasksCompleted++;
    
    if (success) {
      // Mise à jour du taux de succès
      const total = this.state.performance.tasksCompleted;
      const currentSuccessRate = this.state.performance.successRate;
      this.state.performance.successRate = ((currentSuccessRate * (total - 1)) + 100) / total;
    } else {
      // Diminution du taux de succès
      const total = this.state.performance.tasksCompleted;
      const currentSuccessRate = this.state.performance.successRate;
      this.state.performance.successRate = (currentSuccessRate * (total - 1)) / total;
    }
  }
}

export default {{agentName}};
            `,
            variables: [
              'agentName', 'description', 'responsibilities', 'capabilities', 'signalTypes'
            ]
          },
          {
            path: 'src/types/AgentTypes.ts',
            content: `
export interface {{agentName}}Config {
  capabilities: string[];
  maxConcurrentTasks: number;
  responseTimeout: number;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  {{#each configFields}}
  {{name}}: {{type}};
  {{/each}}
}

export interface {{agentName}}State {
  isActive: boolean;
  lastActivity: Date;
  capabilities: string[];
  performance: PerformanceMetrics;
  {{#each stateFields}}
  {{name}}: {{type}};
  {{/each}}
}

export interface PerformanceMetrics {
  tasksCompleted: number;
  averageResponseTime: number;
  successRate: number;
}

export interface AgentCapability {
  name: string;
  description: string;
  enabled: boolean;
  configuration: Record<string, any>;
}
            `,
            variables: ['agentName', 'configFields', 'stateFields']
          },
          {
            path: 'src/components/{{agentName}}Dashboard.tsx',
            content: `
import React, { useState, useEffect } from 'react';
import { {{agentName}} } from '../{{agentName}}';

interface {{agentName}}DashboardProps {
  agent: {{agentName}};
  onAction?: (action: string, data: any) => void;
}

export const {{agentName}}Dashboard: React.FC<{{agentName}}DashboardProps> = ({
  agent,
  onAction
}) => {
  const [state, setState] = useState(agent.getState());
  const [metrics, setMetrics] = useState(agent.getPerformanceMetrics());

  useEffect(() => {
    const updateState = () => {
      setState(agent.getState());
      setMetrics(agent.getPerformanceMetrics());
    };

    agent.on('agent:state-changed', updateState);
    const interval = setInterval(updateState, 5000);

    return () => {
      agent.off('agent:state-changed', updateState);
      clearInterval(interval);
    };
  }, [agent]);

  const handleActivation = async () => {
    try {
      if (state.isActive) {
        await agent.deactivate();
      } else {
        await agent.activate();
      }
      onAction?.('activation-toggled', { active: !state.isActive });
    } catch (error) {
      console.error('Erreur lors de l\\'activation/désactivation:', error);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow border p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">
          🤖 Agent {{agentName}}
        </h2>
        <div className={\`px-3 py-1 rounded-full text-sm font-medium \${
          state.isActive 
            ? 'bg-green-100 text-green-800' 
            : 'bg-gray-100 text-gray-800'
        }\`}>
          {state.isActive ? 'Actif' : 'Inactif'}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-800">Tâches</h3>
          <p className="text-2xl font-bold text-blue-600">
            {metrics.tasksCompleted}
          </p>
        </div>
        
        <div className="bg-green-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-green-800">Succès</h3>
          <p className="text-2xl font-bold text-green-600">
            {metrics.successRate.toFixed(1)}%
          </p>
        </div>
        
        <div className="bg-purple-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-purple-800">Uptime</h3>
          <p className="text-2xl font-bold text-purple-600">
            {(metrics.uptime / 1000 / 60).toFixed(0)}min
          </p>
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-700 mb-2">
            Capacités
          </h3>
          <div className="flex flex-wrap gap-2">
            {state.capabilities.map(capability => (
              <span
                key={capability}
                className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
              >
                {capability}
              </span>
            ))}
          </div>
        </div>

        <div className="flex justify-end space-x-3">
          <button
            onClick={handleActivation}
            className={\`px-4 py-2 rounded-md font-medium \${
              state.isActive
                ? 'bg-red-600 text-white hover:bg-red-700'
                : 'bg-green-600 text-white hover:bg-green-700'
            }\`}
          >
            {state.isActive ? 'Désactiver' : 'Activer'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default {{agentName}}Dashboard;
            `,
            variables: ['agentName']
          }
        ],
        dependencies: [
          'react',
          'react-dom',
          '@types/react',
          '@types/react-dom',
          'typescript',
          'events',
          '@types/node'
        ],
        scripts: {
          'build': 'tsc',
          'dev': 'ts-node src/index.ts',
          'test': 'jest',
          'test:watch': 'jest --watch',
          'lint': 'eslint src --ext .ts,.tsx',
          'lint:fix': 'eslint src --ext .ts,.tsx --fix'
        },
        configuration: {
          environment: {
            'NODE_ENV': 'development',
            'HANUMAN_AGENT_NAME': '{{agentName}}',
            'LOG_LEVEL': 'info'
          },
          buildConfig: {
            target: 'ES2020',
            module: 'CommonJS',
            outDir: 'dist'
          },
          testConfig: {
            testEnvironment: 'node',
            collectCoverage: true,
            coverageDirectory: 'coverage'
          },
          lintConfig: {
            extends: ['@typescript-eslint/recommended'],
            rules: {
              '@typescript-eslint/no-unused-vars': 'error'
            }
          },
          typeScriptConfig: {
            compilerOptions: {
              target: 'ES2020',
              module: 'CommonJS',
              strict: true,
              esModuleInterop: true,
              skipLibCheck: true,
              forceConsistentCasingInFileNames: true
            }
          }
        },
        documentation: {
          readme: `
# Agent {{agentName}}

{{description}}

## Fonctionnalités

- {{#each capabilities}}
- {{.}}
{{/each}}

## Installation

\`\`\`bash
npm install
npm run build
\`\`\`

## Utilisation

\`\`\`typescript
import { {{agentName}} } from './src/{{agentName}}';

const agent = new {{agentName}}(orchestrator, config);
await agent.activate();
\`\`\`

## Configuration

Voir \`src/types/AgentTypes.ts\` pour les options de configuration.

## Tests

\`\`\`bash
npm test
\`\`\`
          `,
          setup: [
            'Installer les dépendances avec npm install',
            'Configurer les variables d\'environnement',
            'Compiler avec npm run build',
            'Lancer les tests avec npm test'
          ],
          usage: [
            'Importer la classe agent',
            'Créer une instance avec la configuration',
            'Activer l\'agent',
            'Écouter les événements'
          ],
          examples: [
            'Création d\'agent basique',
            'Configuration avancée',
            'Intégration avec orchestrateur',
            'Gestion des erreurs'
          ],
          troubleshooting: {
            'Agent ne démarre pas': 'Vérifier la configuration et les dépendances',
            'Erreurs de communication': 'Vérifier la connexion à l\'orchestrateur',
            'Performance dégradée': 'Analyser les métriques et optimiser'
          }
        }
      }
    ];

    defaultTemplates.forEach(template => {
      this.templates.set(template.id, template);
    });

    console.log(`📋 ${defaultTemplates.length} templates par défaut chargés`);
  }

  /**
   * Génère un projet à partir d'un template
   */
  async generateProject(options: TemplateGenerationOptions): Promise<string[]> {
    const template = this.templates.get(options.templateId);
    if (!template) {
      throw new Error(`Template ${options.templateId} non trouvé`);
    }

    const generatedFiles: string[] = [];

    try {
      this.emit('generation:started', options);

      // Créer la structure de répertoires
      await this.createDirectoryStructure(template, options);

      // Générer les fichiers
      for (const file of template.files) {
        const filePath = await this.generateFile(file, template, options);
        generatedFiles.push(filePath);
      }

      // Générer les fichiers de configuration
      await this.generateConfigurationFiles(template, options);

      // Générer la documentation
      await this.generateDocumentation(template, options);

      this.emit('generation:completed', { options, generatedFiles });
      console.log(`✅ Projet généré avec succès: ${generatedFiles.length} fichiers créés`);

      return generatedFiles;

    } catch (error) {
      this.emit('generation:error', { options, error });
      console.error(`❌ Erreur lors de la génération:`, error);
      throw error;
    }
  }

  /**
   * Crée la structure de répertoires
   */
  private async createDirectoryStructure(template: ProjectTemplate, options: TemplateGenerationOptions): Promise<void> {
    // Simuler la création de répertoires
    console.log(`📁 Création de la structure de répertoires...`);
    
    for (const dir of template.structure.directories) {
      const dirPath = `${options.outputPath}/${dir}`;
      console.log(`   - ${dirPath}`);
    }
  }

  /**
   * Génère un fichier à partir d'un template
   */
  private async generateFile(file: TemplateFile, template: ProjectTemplate, options: TemplateGenerationOptions): Promise<string> {
    // Remplacer les variables dans le chemin
    let filePath = this.replaceVariables(file.path, options.variables);
    filePath = `${options.outputPath}/${filePath}`;

    // Remplacer les variables dans le contenu
    const content = this.replaceVariables(file.content, options.variables);

    // Simuler l'écriture du fichier
    console.log(`📄 Génération de ${filePath}`);

    return filePath;
  }

  /**
   * Remplace les variables dans une chaîne
   */
  private replaceVariables(template: string, variables: Record<string, any>): string {
    let result = template;

    // Remplacer les variables simples {{variable}}
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, String(value));
    }

    // Traiter les boucles {{#each array}}
    result = this.processLoops(result, variables);

    // Traiter les conditions {{#if condition}}
    result = this.processConditions(result, variables);

    return result;
  }

  /**
   * Traite les boucles dans les templates
   */
  private processLoops(template: string, variables: Record<string, any>): string {
    const loopRegex = /{{#each\s+(\w+)}}([\s\S]*?){{\/each}}/g;
    
    return template.replace(loopRegex, (match, arrayName, content) => {
      const array = variables[arrayName];
      if (!Array.isArray(array)) return '';

      return array.map(item => {
        let itemContent = content;
        if (typeof item === 'object') {
          for (const [key, value] of Object.entries(item)) {
            itemContent = itemContent.replace(new RegExp(`{{${key}}}`, 'g'), String(value));
          }
        } else {
          itemContent = itemContent.replace(/{{\.}}/g, String(item));
        }
        return itemContent;
      }).join('');
    });
  }

  /**
   * Traite les conditions dans les templates
   */
  private processConditions(template: string, variables: Record<string, any>): string {
    const conditionRegex = /{{#if\s+(\w+)}}([\s\S]*?){{\/if}}/g;
    
    return template.replace(conditionRegex, (match, condition, content) => {
      return variables[condition] ? content : '';
    });
  }

  /**
   * Génère les fichiers de configuration
   */
  private async generateConfigurationFiles(template: ProjectTemplate, options: TemplateGenerationOptions): Promise<void> {
    console.log(`⚙️ Génération des fichiers de configuration...`);
    
    // package.json
    const packageJson = {
      name: options.projectName.toLowerCase().replace(/\s+/g, '-'),
      version: '1.0.0',
      description: template.description,
      scripts: template.scripts,
      dependencies: template.dependencies.reduce((acc, dep) => {
        acc[dep] = 'latest';
        return acc;
      }, {} as Record<string, string>)
    };

    console.log(`   - package.json`);

    // tsconfig.json
    console.log(`   - tsconfig.json`);

    // Autres fichiers de configuration
    console.log(`   - jest.config.js`);
    console.log(`   - .eslintrc.js`);
  }

  /**
   * Génère la documentation
   */
  private async generateDocumentation(template: ProjectTemplate, options: TemplateGenerationOptions): Promise<void> {
    console.log(`📚 Génération de la documentation...`);
    
    const readme = this.replaceVariables(template.documentation.readme, options.variables);
    console.log(`   - README.md`);
  }

  /**
   * Obtient tous les templates
   */
  getTemplates(): ProjectTemplate[] {
    return Array.from(this.templates.values());
  }

  /**
   * Obtient les templates par catégorie
   */
  getTemplatesByCategory(category: ProjectTemplate['category']): ProjectTemplate[] {
    return Array.from(this.templates.values())
      .filter(t => t.category === category);
  }

  /**
   * Obtient un template par ID
   */
  getTemplate(templateId: string): ProjectTemplate | undefined {
    return this.templates.get(templateId);
  }

  /**
   * Ajoute un template personnalisé
   */
  addTemplate(template: ProjectTemplate): void {
    this.templates.set(template.id, template);
    this.emit('template:added', template);
  }

  /**
   * Supprime un template
   */
  removeTemplate(templateId: string): boolean {
    const deleted = this.templates.delete(templateId);
    if (deleted) {
      this.emit('template:removed', templateId);
    }
    return deleted;
  }

  /**
   * Valide les variables d'un template
   */
  validateTemplateVariables(templateId: string, variables: Record<string, any>): string[] {
    const template = this.templates.get(templateId);
    if (!template) {
      throw new Error(`Template ${templateId} non trouvé`);
    }

    const errors: string[] = [];
    
    // Extraire toutes les variables requises du template
    const requiredVariables = this.extractRequiredVariables(template);
    
    for (const variable of requiredVariables) {
      if (!(variable in variables)) {
        errors.push(`Variable requise manquante: ${variable}`);
      }
    }

    return errors;
  }

  /**
   * Extrait les variables requises d'un template
   */
  private extractRequiredVariables(template: ProjectTemplate): string[] {
    const variables = new Set<string>();
    
    // Analyser tous les fichiers du template
    for (const file of template.files) {
      const fileVariables = this.extractVariablesFromString(file.content);
      fileVariables.forEach(v => variables.add(v));
      
      const pathVariables = this.extractVariablesFromString(file.path);
      pathVariables.forEach(v => variables.add(v));
    }

    return Array.from(variables);
  }

  /**
   * Extrait les variables d'une chaîne
   */
  private extractVariablesFromString(str: string): string[] {
    const variableRegex = /{{(\w+)}}/g;
    const variables: string[] = [];
    let match;

    while ((match = variableRegex.exec(str)) !== null) {
      if (!variables.includes(match[1])) {
        variables.push(match[1]);
      }
    }

    return variables;
  }
}

export default TemplateManager;
