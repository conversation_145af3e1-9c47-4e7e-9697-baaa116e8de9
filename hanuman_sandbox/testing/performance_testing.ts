import { SandboxInfrastructure, SandboxContainer } from '../infrastructure/sandbox_infrastructure';
import { TestCase, TestResult } from './automated_testing_framework';

/**
 * Système de Tests de Performance pour la Sandbox Hanuman
 * Gère les tests de charge, profiling des ressources et métriques de latence
 */

export interface PerformanceMetrics {
  responseTime: {
    min: number;
    max: number;
    avg: number;
    p50: number;
    p95: number;
    p99: number;
  };
  throughput: {
    requestsPerSecond: number;
    operationsPerSecond: number;
    dataTransferRate: number; // MB/s
  };
  resources: {
    cpu: {
      usage: number; // percentage
      cores: number;
      loadAverage: number[];
    };
    memory: {
      used: number; // bytes
      available: number; // bytes
      usage: number; // percentage
      heapUsed: number; // bytes
      heapTotal: number; // bytes
    };
    network: {
      bytesIn: number;
      bytesOut: number;
      packetsIn: number;
      packetsOut: number;
      latency: number; // ms
    };
    storage: {
      readOps: number;
      writeOps: number;
      readBytes: number;
      writeBytes: number;
      iops: number;
    };
  };
  errors: {
    total: number;
    rate: number; // percentage
    types: { [errorType: string]: number };
  };
  concurrency: {
    activeConnections: number;
    maxConnections: number;
    queueLength: number;
  };
}

export interface PerformanceTest {
  id: string;
  name: string;
  description: string;
  type: 'load' | 'stress' | 'spike' | 'volume' | 'endurance' | 'scalability';
  configuration: {
    duration: number; // seconds
    rampUpTime: number; // seconds
    rampDownTime: number; // seconds
    virtualUsers: number;
    requestsPerSecond?: number;
    dataSize?: number; // bytes
    iterations?: number;
  };
  target: {
    endpoint?: string;
    function?: string;
    component?: string;
    container?: string;
  };
  thresholds: {
    maxResponseTime: number; // ms
    minThroughput: number; // req/s
    maxErrorRate: number; // percentage
    maxCpuUsage: number; // percentage
    maxMemoryUsage: number; // percentage
  };
  scenarios: PerformanceScenario[];
}

export interface PerformanceScenario {
  id: string;
  name: string;
  weight: number; // percentage of total load
  steps: PerformanceStep[];
}

export interface PerformanceStep {
  id: string;
  name: string;
  action: 'request' | 'wait' | 'think' | 'validate';
  parameters: {
    url?: string;
    method?: string;
    payload?: any;
    headers?: { [key: string]: string };
    duration?: number; // ms
    validation?: (response: any) => boolean;
  };
}

export interface PerformanceTestResult {
  testId: string;
  startTime: Date;
  endTime: Date;
  duration: number;
  status: 'passed' | 'failed' | 'warning';
  metrics: PerformanceMetrics;
  thresholdViolations: {
    threshold: string;
    expected: number;
    actual: number;
    severity: 'low' | 'medium' | 'high' | 'critical';
  }[];
  recommendations: string[];
  rawData: {
    timestamps: number[];
    responseTimes: number[];
    throughput: number[];
    errors: any[];
    resourceUsage: any[];
  };
}

export class PerformanceTesting {
  private infrastructure: SandboxInfrastructure;
  private activeTests: Map<string, PerformanceTest> = new Map();
  private testResults: Map<string, PerformanceTestResult> = new Map();
  private monitoringInterval: NodeJS.Timeout | null = null;

  constructor(infrastructure: SandboxInfrastructure) {
    this.infrastructure = infrastructure;
  }

  /**
   * Exécute un test de performance
   */
  async executePerformanceTest(test: PerformanceTest): Promise<PerformanceTestResult> {
    console.log(`🚀 Démarrage du test de performance: ${test.name}`);
    
    const result: PerformanceTestResult = {
      testId: test.id,
      startTime: new Date(),
      endTime: new Date(),
      duration: 0,
      status: 'passed',
      metrics: this.initializeMetrics(),
      thresholdViolations: [],
      recommendations: [],
      rawData: {
        timestamps: [],
        responseTimes: [],
        throughput: [],
        errors: [],
        resourceUsage: []
      }
    };

    this.activeTests.set(test.id, test);

    try {
      // Démarrer le monitoring des ressources
      await this.startResourceMonitoring(test.id, result);

      // Phase de montée en charge
      if (test.configuration.rampUpTime > 0) {
        await this.executeRampUp(test, result);
      }

      // Phase de test principal
      await this.executeMainTest(test, result);

      // Phase de descente en charge
      if (test.configuration.rampDownTime > 0) {
        await this.executeRampDown(test, result);
      }

      // Arrêter le monitoring
      await this.stopResourceMonitoring();

      // Calculer les métriques finales
      this.calculateFinalMetrics(result);

      // Vérifier les seuils
      this.checkThresholds(test, result);

      // Générer les recommandations
      this.generateRecommendations(test, result);

      result.status = result.thresholdViolations.length === 0 ? 'passed' : 
                     result.thresholdViolations.some(v => v.severity === 'critical') ? 'failed' : 'warning';

    } catch (error) {
      console.error(`❌ Erreur lors du test de performance:`, error);
      result.status = 'failed';
      result.recommendations.push(`Erreur d'exécution: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    } finally {
      result.endTime = new Date();
      result.duration = result.endTime.getTime() - result.startTime.getTime();
      
      this.activeTests.delete(test.id);
      this.testResults.set(test.id, result);
      
      console.log(`✅ Test de performance terminé: ${test.name} (${result.status})`);
    }

    return result;
  }

  /**
   * Initialise les métriques de performance
   */
  private initializeMetrics(): PerformanceMetrics {
    return {
      responseTime: { min: 0, max: 0, avg: 0, p50: 0, p95: 0, p99: 0 },
      throughput: { requestsPerSecond: 0, operationsPerSecond: 0, dataTransferRate: 0 },
      resources: {
        cpu: { usage: 0, cores: 0, loadAverage: [] },
        memory: { used: 0, available: 0, usage: 0, heapUsed: 0, heapTotal: 0 },
        network: { bytesIn: 0, bytesOut: 0, packetsIn: 0, packetsOut: 0, latency: 0 },
        storage: { readOps: 0, writeOps: 0, readBytes: 0, writeBytes: 0, iops: 0 }
      },
      errors: { total: 0, rate: 0, types: {} },
      concurrency: { activeConnections: 0, maxConnections: 0, queueLength: 0 }
    };
  }

  /**
   * Démarre le monitoring des ressources
   */
  private async startResourceMonitoring(testId: string, result: PerformanceTestResult): Promise<void> {
    this.monitoringInterval = setInterval(async () => {
      try {
        const timestamp = Date.now();
        const resourceUsage = await this.collectResourceMetrics();
        
        result.rawData.timestamps.push(timestamp);
        result.rawData.resourceUsage.push(resourceUsage);
        
        // Mettre à jour les métriques en temps réel
        this.updateResourceMetrics(result.metrics, resourceUsage);
        
      } catch (error) {
        console.error('Erreur lors de la collecte des métriques:', error);
      }
    }, 1000); // Collecte toutes les secondes
  }

  /**
   * Arrête le monitoring des ressources
   */
  private async stopResourceMonitoring(): Promise<void> {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
  }

  /**
   * Collecte les métriques de ressources système
   */
  private async collectResourceMetrics(): Promise<any> {
    // Simulation de collecte de métriques (à remplacer par de vraies métriques système)
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    return {
      timestamp: Date.now(),
      cpu: {
        usage: Math.random() * 100,
        cores: require('os').cpus().length,
        loadAverage: require('os').loadavg()
      },
      memory: {
        used: memoryUsage.heapUsed,
        available: memoryUsage.heapTotal,
        usage: (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100,
        heapUsed: memoryUsage.heapUsed,
        heapTotal: memoryUsage.heapTotal
      },
      network: {
        bytesIn: Math.floor(Math.random() * 1000000),
        bytesOut: Math.floor(Math.random() * 1000000),
        packetsIn: Math.floor(Math.random() * 1000),
        packetsOut: Math.floor(Math.random() * 1000),
        latency: Math.random() * 100
      },
      storage: {
        readOps: Math.floor(Math.random() * 100),
        writeOps: Math.floor(Math.random() * 100),
        readBytes: Math.floor(Math.random() * 1000000),
        writeBytes: Math.floor(Math.random() * 1000000),
        iops: Math.floor(Math.random() * 1000)
      }
    };
  }

  /**
   * Met à jour les métriques de ressources
   */
  private updateResourceMetrics(metrics: PerformanceMetrics, resourceUsage: any): void {
    // Mettre à jour les métriques CPU
    metrics.resources.cpu.usage = Math.max(metrics.resources.cpu.usage, resourceUsage.cpu.usage);
    metrics.resources.cpu.cores = resourceUsage.cpu.cores;
    metrics.resources.cpu.loadAverage = resourceUsage.cpu.loadAverage;

    // Mettre à jour les métriques mémoire
    metrics.resources.memory.used = Math.max(metrics.resources.memory.used, resourceUsage.memory.used);
    metrics.resources.memory.available = resourceUsage.memory.available;
    metrics.resources.memory.usage = Math.max(metrics.resources.memory.usage, resourceUsage.memory.usage);
    metrics.resources.memory.heapUsed = resourceUsage.memory.heapUsed;
    metrics.resources.memory.heapTotal = resourceUsage.memory.heapTotal;

    // Mettre à jour les métriques réseau
    metrics.resources.network.bytesIn += resourceUsage.network.bytesIn;
    metrics.resources.network.bytesOut += resourceUsage.network.bytesOut;
    metrics.resources.network.packetsIn += resourceUsage.network.packetsIn;
    metrics.resources.network.packetsOut += resourceUsage.network.packetsOut;
    metrics.resources.network.latency = Math.max(metrics.resources.network.latency, resourceUsage.network.latency);

    // Mettre à jour les métriques stockage
    metrics.resources.storage.readOps += resourceUsage.storage.readOps;
    metrics.resources.storage.writeOps += resourceUsage.storage.writeOps;
    metrics.resources.storage.readBytes += resourceUsage.storage.readBytes;
    metrics.resources.storage.writeBytes += resourceUsage.storage.writeBytes;
    metrics.resources.storage.iops = Math.max(metrics.resources.storage.iops, resourceUsage.storage.iops);
  }

  /**
   * Exécute la phase de montée en charge
   */
  private async executeRampUp(test: PerformanceTest, result: PerformanceTestResult): Promise<void> {
    console.log(`📈 Phase de montée en charge: ${test.configuration.rampUpTime}s`);
    
    const rampUpSteps = 10;
    const stepDuration = test.configuration.rampUpTime * 1000 / rampUpSteps;
    const usersPerStep = test.configuration.virtualUsers / rampUpSteps;

    for (let step = 1; step <= rampUpSteps; step++) {
      const currentUsers = Math.floor(usersPerStep * step);
      console.log(`  Étape ${step}/${rampUpSteps}: ${currentUsers} utilisateurs virtuels`);
      
      // Simuler la charge progressive
      await this.simulateLoad(currentUsers, stepDuration, result);
      
      await this.sleep(stepDuration);
    }
  }

  /**
   * Exécute le test principal
   */
  private async executeMainTest(test: PerformanceTest, result: PerformanceTestResult): Promise<void> {
    console.log(`🎯 Phase de test principal: ${test.configuration.duration}s avec ${test.configuration.virtualUsers} utilisateurs`);
    
    const testDuration = test.configuration.duration * 1000;
    await this.simulateLoad(test.configuration.virtualUsers, testDuration, result);
  }

  /**
   * Exécute la phase de descente en charge
   */
  private async executeRampDown(test: PerformanceTest, result: PerformanceTestResult): Promise<void> {
    console.log(`📉 Phase de descente en charge: ${test.configuration.rampDownTime}s`);
    
    const rampDownSteps = 10;
    const stepDuration = test.configuration.rampDownTime * 1000 / rampDownSteps;
    const usersPerStep = test.configuration.virtualUsers / rampDownSteps;

    for (let step = rampDownSteps; step >= 1; step--) {
      const currentUsers = Math.floor(usersPerStep * step);
      console.log(`  Étape ${rampDownSteps - step + 1}/${rampDownSteps}: ${currentUsers} utilisateurs virtuels`);
      
      await this.simulateLoad(currentUsers, stepDuration, result);
      await this.sleep(stepDuration);
    }
  }

  /**
   * Simule une charge de travail
   */
  private async simulateLoad(virtualUsers: number, duration: number, result: PerformanceTestResult): Promise<void> {
    const startTime = Date.now();
    const endTime = startTime + duration;
    const promises: Promise<void>[] = [];

    // Créer les utilisateurs virtuels
    for (let i = 0; i < virtualUsers; i++) {
      promises.push(this.simulateVirtualUser(i, endTime, result));
    }

    // Attendre que tous les utilisateurs virtuels terminent
    await Promise.allSettled(promises);
  }

  /**
   * Simule un utilisateur virtuel
   */
  private async simulateVirtualUser(userId: number, endTime: number, result: PerformanceTestResult): Promise<void> {
    while (Date.now() < endTime) {
      const requestStart = Date.now();
      
      try {
        // Simuler une requête (à remplacer par de vraies requêtes)
        await this.simulateRequest();
        
        const responseTime = Date.now() - requestStart;
        result.rawData.responseTimes.push(responseTime);
        
        // Mettre à jour les métriques de débit
        result.metrics.throughput.requestsPerSecond++;
        
      } catch (error) {
        result.metrics.errors.total++;
        result.rawData.errors.push({
          timestamp: Date.now(),
          userId,
          error: error instanceof Error ? error.message : 'Erreur inconnue'
        });
      }
      
      // Temps de réflexion entre les requêtes
      await this.sleep(Math.random() * 1000 + 500);
    }
  }

  /**
   * Simule une requête
   */
  private async simulateRequest(): Promise<void> {
    // Simulation d'une requête avec latence variable
    const latency = Math.random() * 200 + 50; // 50-250ms
    await this.sleep(latency);
    
    // Simuler des erreurs occasionnelles
    if (Math.random() < 0.05) { // 5% d'erreurs
      throw new Error('Erreur simulée');
    }
  }

  /**
   * Calcule les métriques finales
   */
  private calculateFinalMetrics(result: PerformanceTestResult): void {
    const responseTimes = result.rawData.responseTimes.sort((a, b) => a - b);
    
    if (responseTimes.length > 0) {
      result.metrics.responseTime.min = responseTimes[0];
      result.metrics.responseTime.max = responseTimes[responseTimes.length - 1];
      result.metrics.responseTime.avg = responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length;
      result.metrics.responseTime.p50 = responseTimes[Math.floor(responseTimes.length * 0.5)];
      result.metrics.responseTime.p95 = responseTimes[Math.floor(responseTimes.length * 0.95)];
      result.metrics.responseTime.p99 = responseTimes[Math.floor(responseTimes.length * 0.99)];
    }

    // Calculer le débit moyen
    const durationSeconds = result.duration / 1000;
    if (durationSeconds > 0) {
      result.metrics.throughput.requestsPerSecond = responseTimes.length / durationSeconds;
      result.metrics.throughput.operationsPerSecond = result.metrics.throughput.requestsPerSecond;
    }

    // Calculer le taux d'erreur
    const totalRequests = responseTimes.length + result.metrics.errors.total;
    if (totalRequests > 0) {
      result.metrics.errors.rate = (result.metrics.errors.total / totalRequests) * 100;
    }
  }

  /**
   * Vérifie les seuils de performance
   */
  private checkThresholds(test: PerformanceTest, result: PerformanceTestResult): void {
    const thresholds = test.thresholds;
    const metrics = result.metrics;

    // Vérifier le temps de réponse
    if (metrics.responseTime.avg > thresholds.maxResponseTime) {
      result.thresholdViolations.push({
        threshold: 'Temps de réponse moyen',
        expected: thresholds.maxResponseTime,
        actual: metrics.responseTime.avg,
        severity: metrics.responseTime.avg > thresholds.maxResponseTime * 2 ? 'critical' : 'high'
      });
    }

    // Vérifier le débit
    if (metrics.throughput.requestsPerSecond < thresholds.minThroughput) {
      result.thresholdViolations.push({
        threshold: 'Débit minimum',
        expected: thresholds.minThroughput,
        actual: metrics.throughput.requestsPerSecond,
        severity: metrics.throughput.requestsPerSecond < thresholds.minThroughput * 0.5 ? 'critical' : 'high'
      });
    }

    // Vérifier le taux d'erreur
    if (metrics.errors.rate > thresholds.maxErrorRate) {
      result.thresholdViolations.push({
        threshold: 'Taux d\'erreur maximum',
        expected: thresholds.maxErrorRate,
        actual: metrics.errors.rate,
        severity: metrics.errors.rate > thresholds.maxErrorRate * 2 ? 'critical' : 'medium'
      });
    }

    // Vérifier l'utilisation CPU
    if (metrics.resources.cpu.usage > thresholds.maxCpuUsage) {
      result.thresholdViolations.push({
        threshold: 'Utilisation CPU maximum',
        expected: thresholds.maxCpuUsage,
        actual: metrics.resources.cpu.usage,
        severity: metrics.resources.cpu.usage > 90 ? 'critical' : 'medium'
      });
    }

    // Vérifier l'utilisation mémoire
    if (metrics.resources.memory.usage > thresholds.maxMemoryUsage) {
      result.thresholdViolations.push({
        threshold: 'Utilisation mémoire maximum',
        expected: thresholds.maxMemoryUsage,
        actual: metrics.resources.memory.usage,
        severity: metrics.resources.memory.usage > 90 ? 'critical' : 'medium'
      });
    }
  }

  /**
   * Génère des recommandations d'optimisation
   */
  private generateRecommendations(test: PerformanceTest, result: PerformanceTestResult): void {
    const metrics = result.metrics;
    const recommendations: string[] = [];

    // Recommandations basées sur les temps de réponse
    if (metrics.responseTime.p95 > metrics.responseTime.avg * 2) {
      recommendations.push('Optimiser les requêtes les plus lentes (P95 élevé)');
    }

    // Recommandations basées sur l'utilisation des ressources
    if (metrics.resources.cpu.usage > 80) {
      recommendations.push('Optimiser l\'utilisation CPU ou augmenter les ressources');
    }

    if (metrics.resources.memory.usage > 80) {
      recommendations.push('Optimiser l\'utilisation mémoire ou augmenter la RAM');
    }

    // Recommandations basées sur les erreurs
    if (metrics.errors.rate > 1) {
      recommendations.push('Investiguer et corriger les erreurs fréquentes');
    }

    // Recommandations basées sur le débit
    if (metrics.throughput.requestsPerSecond < test.thresholds.minThroughput * 0.8) {
      recommendations.push('Améliorer l\'architecture pour augmenter le débit');
    }

    result.recommendations = recommendations;
  }

  /**
   * Utilitaire pour attendre
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Obtient les résultats d'un test
   */
  getTestResult(testId: string): PerformanceTestResult | undefined {
    return this.testResults.get(testId);
  }

  /**
   * Obtient tous les résultats de tests
   */
  getAllTestResults(): PerformanceTestResult[] {
    return Array.from(this.testResults.values());
  }

  /**
   * Obtient les tests actifs
   */
  getActiveTests(): PerformanceTest[] {
    return Array.from(this.activeTests.values());
  }
}
