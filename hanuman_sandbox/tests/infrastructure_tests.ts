import { SandboxInfrastructure } from '../infrastructure/sandbox_infrastructure';
import { SandboxSecurity } from '../security/sandbox_security';
import { HanumanOrganOrchestrator } from '../../hanuman-working/services/HanumanOrganOrchestrator';

/**
 * Tests pour l'Infrastructure de la Sandbox Hanuman
 * Valide la création, l'isolation et la sécurité des conteneurs
 */

// Mock de l'orchestrateur pour les tests
class MockHanumanOrchestrator {
  emit(event: string, data: any) {
    console.log(`Mock Orchestrator Event: ${event}`, data);
  }
  
  on(event: string, handler: Function) {
    console.log(`Mock Orchestrator Listener: ${event}`);
  }
  
  off(event: string, handler: Function) {
    console.log(`Mock Orchestrator Remove Listener: ${event}`);
  }
}

export class InfrastructureTests {
  private infrastructure: SandboxInfrastructure;
  private security: SandboxSecurity;
  private mockOrchestrator: MockHanumanOrchestrator;
  private testResults: { [key: string]: boolean } = {};

  constructor() {
    this.mockOrchestrator = new MockHanumanOrchestrator();
    this.infrastructure = new SandboxInfrastructure(this.mockOrchestrator as any);
    this.security = new SandboxSecurity(this.infrastructure);
  }

  /**
   * Lance tous les tests d'infrastructure
   */
  async runAllTests(): Promise<{ passed: number; failed: number; results: { [key: string]: boolean } }> {
    console.log('🧪 Démarrage des tests d\'infrastructure Sandbox Hanuman...\n');

    const tests = [
      { name: 'Infrastructure Initialization', test: () => this.testInfrastructureInitialization() },
      { name: 'Container Creation', test: () => this.testContainerCreation() },
      { name: 'Container Isolation', test: () => this.testContainerIsolation() },
      { name: 'Security Policies', test: () => this.testSecurityPolicies() },
      { name: 'Resource Management', test: () => this.testResourceManagement() },
      { name: 'Network Isolation', test: () => this.testNetworkIsolation() },
      { name: 'Storage Encryption', test: () => this.testStorageEncryption() },
      { name: 'Monitoring System', test: () => this.testMonitoringSystem() },
      { name: 'Incident Detection', test: () => this.testIncidentDetection() },
      { name: 'Container Cleanup', test: () => this.testContainerCleanup() }
    ];

    let passed = 0;
    let failed = 0;

    for (const { name, test } of tests) {
      try {
        console.log(`🔍 Test: ${name}`);
        const result = await test();
        this.testResults[name] = result;
        
        if (result) {
          console.log(`✅ ${name}: PASSED\n`);
          passed++;
        } else {
          console.log(`❌ ${name}: FAILED\n`);
          failed++;
        }
      } catch (error) {
        console.log(`💥 ${name}: ERROR - ${error}\n`);
        this.testResults[name] = false;
        failed++;
      }
    }

    console.log(`📊 Résultats des tests:`);
    console.log(`✅ Tests réussis: ${passed}`);
    console.log(`❌ Tests échoués: ${failed}`);
    console.log(`📈 Taux de réussite: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);

    return { passed, failed, results: this.testResults };
  }

  /**
   * Test d'initialisation de l'infrastructure
   */
  private async testInfrastructureInitialization(): Promise<boolean> {
    // Attendre que l'infrastructure soit initialisée
    await new Promise(resolve => setTimeout(resolve, 1000));

    const stats = this.infrastructure.getInfrastructureStats();
    const namespaces = this.infrastructure.getNamespaces();

    // Vérifier que l'infrastructure est initialisée
    if (!stats.isInitialized) {
      console.log('   ❌ Infrastructure non initialisée');
      return false;
    }

    // Vérifier que les namespaces par défaut sont créés
    if (namespaces.length < 3) {
      console.log('   ❌ Namespaces par défaut manquants');
      return false;
    }

    const expectedNamespaces = ['development', 'testing', 'security'];
    const actualNamespaces = namespaces.map(ns => ns.name);
    
    for (const expected of expectedNamespaces) {
      if (!actualNamespaces.includes(expected)) {
        console.log(`   ❌ Namespace manquant: ${expected}`);
        return false;
      }
    }

    console.log('   ✅ Infrastructure correctement initialisée');
    console.log(`   ✅ ${namespaces.length} namespaces créés`);
    return true;
  }

  /**
   * Test de création de conteneur
   */
  private async testContainerCreation(): Promise<boolean> {
    try {
      const container = await this.infrastructure.createContainer({
        name: 'test-container-dev',
        type: 'development',
        namespace: 'development',
        agentId: 'test-agent',
        securityLevel: 'medium'
      });

      // Vérifier que le conteneur est créé
      if (!container.id) {
        console.log('   ❌ Conteneur sans ID');
        return false;
      }

      if (container.status !== 'running') {
        console.log(`   ❌ Statut incorrect: ${container.status}`);
        return false;
      }

      if (container.type !== 'development') {
        console.log(`   ❌ Type incorrect: ${container.type}`);
        return false;
      }

      // Vérifier que le conteneur est dans la liste
      const containers = this.infrastructure.getContainers();
      const foundContainer = containers.find(c => c.id === container.id);
      
      if (!foundContainer) {
        console.log('   ❌ Conteneur non trouvé dans la liste');
        return false;
      }

      console.log(`   ✅ Conteneur créé: ${container.name} (${container.id})`);
      console.log(`   ✅ Ressources allouées: ${container.resources.cpu}c/${container.resources.memory}MB`);
      return true;

    } catch (error) {
      console.log(`   ❌ Erreur lors de la création: ${error}`);
      return false;
    }
  }

  /**
   * Test d'isolation des conteneurs
   */
  private async testContainerIsolation(): Promise<boolean> {
    try {
      // Créer un conteneur avec isolation maximale
      const container = await this.infrastructure.createContainer({
        name: 'test-container-isolated',
        type: 'security',
        namespace: 'security',
        securityLevel: 'maximum'
      });

      // Vérifier l'isolation réseau
      if (!container.isolation.networkIsolated) {
        console.log('   ❌ Isolation réseau non activée');
        return false;
      }

      // Vérifier l'isolation des processus
      if (!container.isolation.processIsolated) {
        console.log('   ❌ Isolation des processus non activée');
        return false;
      }

      // Vérifier le chiffrement du stockage pour niveau maximum
      if (container.isolation.securityLevel === 'maximum' && !container.isolation.storageEncrypted) {
        console.log('   ❌ Stockage non chiffré pour niveau maximum');
        return false;
      }

      // Vérifier le réseau assigné
      if (container.resources.network !== 'sandbox-isolated') {
        console.log(`   ❌ Réseau incorrect: ${container.resources.network}`);
        return false;
      }

      console.log('   ✅ Isolation réseau activée');
      console.log('   ✅ Isolation des processus activée');
      console.log('   ✅ Stockage chiffré pour niveau maximum');
      return true;

    } catch (error) {
      console.log(`   ❌ Erreur lors du test d'isolation: ${error}`);
      return false;
    }
  }

  /**
   * Test des politiques de sécurité
   */
  private async testSecurityPolicies(): Promise<boolean> {
    const policies = this.security.getPolicies();

    // Vérifier qu'il y a des politiques par défaut
    if (policies.length === 0) {
      console.log('   ❌ Aucune politique de sécurité trouvée');
      return false;
    }

    // Vérifier les types de politiques essentielles
    const requiredTypes = ['network', 'process', 'file', 'resource', 'access'];
    const actualTypes = policies.map(p => p.type);

    for (const requiredType of requiredTypes) {
      if (!actualTypes.includes(requiredType)) {
        console.log(`   ❌ Politique manquante: ${requiredType}`);
        return false;
      }
    }

    // Vérifier qu'au moins une politique critique est activée
    const criticalPolicies = policies.filter(p => p.severity === 'critical' && p.enabled);
    if (criticalPolicies.length === 0) {
      console.log('   ❌ Aucune politique critique activée');
      return false;
    }

    console.log(`   ✅ ${policies.length} politiques de sécurité chargées`);
    console.log(`   ✅ ${criticalPolicies.length} politiques critiques activées`);
    return true;
  }

  /**
   * Test de gestion des ressources
   */
  private async testResourceManagement(): Promise<boolean> {
    try {
      // Créer plusieurs conteneurs pour tester les limites
      const containers = [];
      
      for (let i = 0; i < 3; i++) {
        const container = await this.infrastructure.createContainer({
          name: `test-resource-${i}`,
          type: 'testing',
          namespace: 'testing',
          securityLevel: 'medium'
        });
        containers.push(container);
      }

      // Vérifier l'allocation des ressources
      let totalCpu = 0;
      let totalMemory = 0;
      
      for (const container of containers) {
        totalCpu += container.resources.cpu;
        totalMemory += container.resources.memory;
      }

      // Vérifier que les ressources sont allouées de manière cohérente
      if (totalCpu === 0 || totalMemory === 0) {
        console.log('   ❌ Ressources non allouées');
        return false;
      }

      // Vérifier les limites par type de conteneur
      const testingContainer = containers[0];
      if (testingContainer.resources.cpu !== 2) {
        console.log(`   ❌ Allocation CPU incorrecte pour testing: ${testingContainer.resources.cpu}`);
        return false;
      }

      if (testingContainer.resources.memory !== 2048) {
        console.log(`   ❌ Allocation mémoire incorrecte pour testing: ${testingContainer.resources.memory}`);
        return false;
      }

      console.log(`   ✅ Ressources allouées: ${totalCpu} CPU, ${totalMemory} MB RAM`);
      console.log('   ✅ Allocation par type respectée');
      return true;

    } catch (error) {
      console.log(`   ❌ Erreur lors du test de ressources: ${error}`);
      return false;
    }
  }

  /**
   * Test d'isolation réseau
   */
  private async testNetworkIsolation(): Promise<boolean> {
    try {
      // Créer des conteneurs avec différents niveaux de sécurité
      const devContainer = await this.infrastructure.createContainer({
        name: 'test-network-dev',
        type: 'development',
        namespace: 'development',
        securityLevel: 'low'
      });

      const secContainer = await this.infrastructure.createContainer({
        name: 'test-network-sec',
        type: 'security',
        namespace: 'security',
        securityLevel: 'maximum'
      });

      // Vérifier l'assignation des réseaux
      if (devContainer.resources.network !== 'sandbox-monitored') {
        console.log(`   ❌ Réseau dev incorrect: ${devContainer.resources.network}`);
        return false;
      }

      if (secContainer.resources.network !== 'sandbox-isolated') {
        console.log(`   ❌ Réseau sécurité incorrect: ${secContainer.resources.network}`);
        return false;
      }

      console.log('   ✅ Réseaux assignés selon le niveau de sécurité');
      console.log(`   ✅ Dev: ${devContainer.resources.network}`);
      console.log(`   ✅ Sécurité: ${secContainer.resources.network}`);
      return true;

    } catch (error) {
      console.log(`   ❌ Erreur lors du test réseau: ${error}`);
      return false;
    }
  }

  /**
   * Test de chiffrement du stockage
   */
  private async testStorageEncryption(): Promise<boolean> {
    try {
      // Créer un conteneur avec chiffrement requis
      const container = await this.infrastructure.createContainer({
        name: 'test-storage-encrypted',
        type: 'security',
        namespace: 'security',
        securityLevel: 'high'
      });

      // Vérifier que le chiffrement est activé
      if (!container.isolation.storageEncrypted) {
        console.log('   ❌ Chiffrement du stockage non activé');
        return false;
      }

      console.log('   ✅ Chiffrement du stockage activé pour niveau high');
      return true;

    } catch (error) {
      console.log(`   ❌ Erreur lors du test de chiffrement: ${error}`);
      return false;
    }
  }

  /**
   * Test du système de monitoring
   */
  private async testMonitoringSystem(): Promise<boolean> {
    const metrics = this.security.getSecurityMetrics();

    // Vérifier que les métriques sont initialisées
    if (typeof metrics.securityScore !== 'number') {
      console.log('   ❌ Score de sécurité non initialisé');
      return false;
    }

    if (!(metrics.lastScan instanceof Date)) {
      console.log('   ❌ Date de dernier scan invalide');
      return false;
    }

    if (typeof metrics.policiesActive !== 'number') {
      console.log('   ❌ Nombre de politiques actives invalide');
      return false;
    }

    console.log(`   ✅ Score de sécurité: ${metrics.securityScore}%`);
    console.log(`   ✅ Politiques actives: ${metrics.policiesActive}`);
    console.log(`   ✅ Dernier scan: ${metrics.lastScan.toLocaleString()}`);
    return true;
  }

  /**
   * Test de détection d'incidents
   */
  private async testIncidentDetection(): Promise<boolean> {
    // Attendre un cycle de scan
    await new Promise(resolve => setTimeout(resolve, 2000));

    const incidents = this.security.getIncidents();
    const openIncidents = this.security.getOpenIncidents();

    // Il devrait y avoir au moins quelques incidents détectés (conteneurs inactifs, etc.)
    console.log(`   ✅ ${incidents.length} incidents détectés au total`);
    console.log(`   ✅ ${openIncidents.length} incidents ouverts`);
    
    // Vérifier que le système peut détecter des incidents
    return true;
  }

  /**
   * Test de nettoyage des conteneurs
   */
  private async testContainerCleanup(): Promise<boolean> {
    try {
      // Créer un conteneur temporaire
      const container = await this.infrastructure.createContainer({
        name: 'test-cleanup',
        type: 'development',
        namespace: 'development',
        securityLevel: 'low'
      });

      const containerId = container.id;

      // Vérifier que le conteneur existe
      let foundContainer = this.infrastructure.getContainer(containerId);
      if (!foundContainer) {
        console.log('   ❌ Conteneur non trouvé avant nettoyage');
        return false;
      }

      // Détruire le conteneur
      await this.infrastructure.destroyContainer(containerId);

      // Vérifier que le conteneur est supprimé
      foundContainer = this.infrastructure.getContainer(containerId);
      if (foundContainer) {
        console.log('   ❌ Conteneur toujours présent après destruction');
        return false;
      }

      console.log('   ✅ Conteneur correctement détruit');
      console.log('   ✅ Nettoyage des ressources effectué');
      return true;

    } catch (error) {
      console.log(`   ❌ Erreur lors du test de nettoyage: ${error}`);
      return false;
    }
  }

  /**
   * Test de performance et de charge
   */
  async testPerformance(): Promise<{ creationTime: number; maxContainers: number }> {
    console.log('🚀 Test de performance...\n');

    // Test de temps de création
    const startTime = Date.now();
    
    try {
      await this.infrastructure.createContainer({
        name: 'perf-test',
        type: 'development',
        namespace: 'development',
        securityLevel: 'medium'
      });
      
      const creationTime = Date.now() - startTime;
      console.log(`⏱️ Temps de création d'un conteneur: ${creationTime}ms`);

      // Test de charge - créer plusieurs conteneurs
      const containers = [];
      const batchStartTime = Date.now();
      
      for (let i = 0; i < 5; i++) {
        try {
          const container = await this.infrastructure.createContainer({
            name: `load-test-${i}`,
            type: 'testing',
            namespace: 'testing',
            securityLevel: 'medium'
          });
          containers.push(container);
        } catch (error) {
          console.log(`   ⚠️ Échec création conteneur ${i}: ${error}`);
          break;
        }
      }

      const batchTime = Date.now() - batchStartTime;
      console.log(`⏱️ Temps de création de ${containers.length} conteneurs: ${batchTime}ms`);
      console.log(`📊 Moyenne par conteneur: ${(batchTime / containers.length).toFixed(1)}ms`);

      return {
        creationTime,
        maxContainers: containers.length
      };

    } catch (error) {
      console.log(`❌ Erreur lors du test de performance: ${error}`);
      return { creationTime: -1, maxContainers: 0 };
    }
  }

  /**
   * Génère un rapport de test complet
   */
  generateTestReport(): string {
    const passed = Object.values(this.testResults).filter(r => r).length;
    const total = Object.keys(this.testResults).length;
    const successRate = total > 0 ? (passed / total * 100).toFixed(1) : '0';

    let report = `
🏗️ RAPPORT DE TEST - INFRASTRUCTURE SANDBOX HANUMAN
=====================================================

📊 RÉSUMÉ
---------
Tests exécutés: ${total}
Tests réussis: ${passed}
Tests échoués: ${total - passed}
Taux de réussite: ${successRate}%

📋 DÉTAIL DES TESTS
-------------------
`;

    for (const [testName, result] of Object.entries(this.testResults)) {
      report += `${result ? '✅' : '❌'} ${testName}\n`;
    }

    report += `
🎯 RECOMMANDATIONS
------------------
`;

    if (passed === total) {
      report += `✅ Tous les tests sont passés avec succès !
✅ L'infrastructure de la sandbox est prête pour la production.
✅ Les systèmes de sécurité et d'isolation fonctionnent correctement.`;
    } else {
      report += `⚠️ ${total - passed} test(s) ont échoué.
🔧 Vérifiez les logs pour identifier les problèmes.
🛠️ Corrigez les erreurs avant de passer en production.`;
    }

    return report;
  }
}

// Fonction utilitaire pour lancer les tests
export async function runSandboxTests(): Promise<void> {
  const tester = new InfrastructureTests();
  
  console.log('🏗️ TESTS INFRASTRUCTURE SANDBOX HANUMAN');
  console.log('=========================================\n');
  
  // Tests principaux
  const results = await tester.runAllTests();
  
  // Tests de performance
  console.log('\n🚀 TESTS DE PERFORMANCE');
  console.log('========================\n');
  const perfResults = await tester.testPerformance();
  
  // Rapport final
  console.log('\n📋 RAPPORT FINAL');
  console.log('================');
  console.log(tester.generateTestReport());
  
  console.log('\n🎉 Tests terminés !');
}

// Export pour utilisation directe
export default InfrastructureTests;
