import { EventEmitter } from 'events';
import WebSocket from 'ws';
import axios, { AxiosResponse } from 'axios';

// Types pour les connexions d'agents
export interface AgentConfig {
  id: string;
  name: string;
  type: 'frontend' | 'backend' | 'devops' | 'security' | 'qa' | 'web-research' | 'documentation' | 'marketing' | 'uiux' | 'performance';
  host: string;
  port: number;
  apiPath: string;
  wsPath?: string;
  status: 'active' | 'inactive' | 'error' | 'connecting';
  lastHeartbeat?: Date;
  capabilities: string[];
  metadata?: Record<string, any>;
}

export interface AgentMessage {
  type: string;
  agentId: string;
  timestamp: number;
  data: any;
  correlationId?: string;
}

export interface AgentResponse {
  success: boolean;
  data?: any;
  error?: string;
  timestamp: number;
  agentId: string;
}

/**
 * Gestionnaire central des connexions avec tous les agents Hanuman
 * Intègre les organes sensoriels avec l'architecture neuronale distribuée
 */
export class AgentConnectionManager extends EventEmitter {
  private agents: Map<string, AgentConfig> = new Map();
  private wsConnections: Map<string, WebSocket> = new Map();
  private heartbeatIntervals: Map<string, NodeJS.Timeout> = new Map();
  private reconnectAttempts: Map<string, number> = new Map();
  private maxReconnectAttempts = 5;
  private heartbeatInterval = 30000; // 30 secondes

  constructor() {
    super();
    this.initializeAgents();
    this.startHealthMonitoring();
  }

  /**
   * Initialise la configuration des agents basée sur l'architecture existante
   */
  private initializeAgents(): void {
    const agentConfigs: AgentConfig[] = [
      {
        id: 'agent-frontend',
        name: 'Agent Frontend',
        type: 'frontend',
        host: 'localhost',
        port: 3001,
        apiPath: '/api',
        wsPath: '/ws',
        status: 'inactive',
        capabilities: ['ui-generation', 'component-creation', 'styling', 'responsive-design']
      },
      {
        id: 'agent-backend',
        name: 'Agent Backend',
        type: 'backend',
        host: 'localhost',
        port: 3002,
        apiPath: '/api',
        wsPath: '/ws',
        status: 'inactive',
        capabilities: ['api-development', 'database-design', 'microservices', 'authentication']
      },
      {
        id: 'agent-web-research',
        name: 'Agent Web Research',
        type: 'web-research',
        host: 'localhost',
        port: 3003,
        apiPath: '/api',
        wsPath: '/ws',
        status: 'inactive',
        capabilities: ['web-scraping', 'data-collection', 'trend-analysis', 'content-discovery']
      },
      {
        id: 'agent-devops',
        name: 'Agent DevOps',
        type: 'devops',
        host: 'localhost',
        port: 3004,
        apiPath: '/api',
        wsPath: '/ws',
        status: 'inactive',
        capabilities: ['deployment', 'infrastructure', 'monitoring', 'ci-cd']
      },
      {
        id: 'agent-security',
        name: 'Agent Security',
        type: 'security',
        host: 'localhost',
        port: 3007,
        apiPath: '/api',
        wsPath: '/ws',
        status: 'inactive',
        capabilities: ['vulnerability-scanning', 'threat-detection', 'compliance', 'encryption']
      },
      {
        id: 'agent-qa',
        name: 'Agent QA',
        type: 'qa',
        host: 'localhost',
        port: 3005,
        apiPath: '/api',
        wsPath: '/ws',
        status: 'inactive',
        capabilities: ['testing', 'quality-assurance', 'automation', 'performance-testing']
      },
      {
        id: 'agent-documentation',
        name: 'Agent Documentation',
        type: 'documentation',
        host: 'localhost',
        port: 3006,
        apiPath: '/api',
        wsPath: '/ws',
        status: 'inactive',
        capabilities: ['documentation-generation', 'api-docs', 'user-guides', 'technical-writing']
      },
      {
        id: 'agent-marketing',
        name: 'Agent Marketing',
        type: 'marketing',
        host: 'localhost',
        port: 3008,
        apiPath: '/api',
        wsPath: '/ws',
        status: 'inactive',
        capabilities: ['content-marketing', 'seo', 'analytics', 'campaign-management']
      },
      {
        id: 'agent-uiux',
        name: 'Agent UI/UX',
        type: 'uiux',
        host: 'localhost',
        port: 3009,
        apiPath: '/api',
        wsPath: '/ws',
        status: 'inactive',
        capabilities: ['design-thinking', 'user-research', 'prototyping', 'accessibility']
      },
      {
        id: 'agent-performance',
        name: 'Agent Performance',
        type: 'performance',
        host: 'localhost',
        port: 3010,
        apiPath: '/api',
        wsPath: '/ws',
        status: 'inactive',
        capabilities: ['performance-monitoring', 'optimization', 'metrics-analysis', 'alerting']
      }
    ];

    agentConfigs.forEach(config => {
      this.agents.set(config.id, config);
    });

    console.log(`🧠 Initialized ${agentConfigs.length} agents in Hanuman's neural network`);
  }

  /**
   * Démarre le monitoring de santé de tous les agents
   */
  private startHealthMonitoring(): void {
    setInterval(() => {
      this.checkAllAgentsHealth();
    }, this.heartbeatInterval);

    // Tentative de connexion initiale à tous les agents
    this.connectToAllAgents();
  }

  /**
   * Connecte à tous les agents disponibles
   */
  public async connectToAllAgents(): Promise<void> {
    const connectionPromises = Array.from(this.agents.values()).map(agent => 
      this.connectToAgent(agent.id)
    );

    await Promise.allSettled(connectionPromises);
    this.emit('agents:connection-attempt-completed');
  }

  /**
   * Connecte à un agent spécifique
   */
  public async connectToAgent(agentId: string): Promise<boolean> {
    const agent = this.agents.get(agentId);
    if (!agent) {
      console.error(`❌ Agent ${agentId} not found`);
      return false;
    }

    try {
      // Test de connexion HTTP d'abord
      const healthCheck = await this.performHealthCheck(agent);
      if (!healthCheck) {
        agent.status = 'error';
        this.agents.set(agentId, agent);
        return false;
      }

      // Connexion WebSocket si disponible
      if (agent.wsPath) {
        await this.establishWebSocketConnection(agent);
      }

      agent.status = 'active';
      agent.lastHeartbeat = new Date();
      this.agents.set(agentId, agent);
      
      console.log(`✅ Connected to ${agent.name}`);
      this.emit('agent:connected', agent);
      
      return true;

    } catch (error) {
      console.error(`❌ Failed to connect to ${agent.name}:`, error);
      agent.status = 'error';
      this.agents.set(agentId, agent);
      
      // Programmer une reconnexion
      this.scheduleReconnection(agentId);
      
      return false;
    }
  }

  /**
   * Effectue un health check HTTP sur un agent
   */
  private async performHealthCheck(agent: AgentConfig): Promise<boolean> {
    try {
      const url = `http://${agent.host}:${agent.port}${agent.apiPath}/health`;
      const response: AxiosResponse = await axios.get(url, { 
        timeout: 5000,
        headers: {
          'User-Agent': 'Hanuman-Neural-Network/1.0'
        }
      });
      
      return response.status === 200;
    } catch (error) {
      return false;
    }
  }

  /**
   * Établit une connexion WebSocket avec un agent
   */
  private async establishWebSocketConnection(agent: AgentConfig): Promise<void> {
    return new Promise((resolve, reject) => {
      const wsUrl = `ws://${agent.host}:${agent.port}${agent.wsPath}`;
      const ws = new WebSocket(wsUrl);

      ws.on('open', () => {
        console.log(`🔗 WebSocket connected to ${agent.name}`);
        this.wsConnections.set(agent.id, ws);
        
        // Envoyer un message d'identification
        ws.send(JSON.stringify({
          type: 'HANUMAN_IDENTIFY',
          source: 'hanuman-neural-network',
          timestamp: Date.now()
        }));
        
        resolve();
      });

      ws.on('message', (data: string) => {
        try {
          const message: AgentMessage = JSON.parse(data);
          this.handleAgentMessage(agent.id, message);
        } catch (error) {
          console.error(`❌ Invalid message from ${agent.name}:`, error);
        }
      });

      ws.on('close', () => {
        console.log(`❌ WebSocket disconnected from ${agent.name}`);
        this.wsConnections.delete(agent.id);
        agent.status = 'inactive';
        this.agents.set(agent.id, agent);
        this.emit('agent:disconnected', agent);
        
        // Programmer une reconnexion
        this.scheduleReconnection(agent.id);
      });

      ws.on('error', (error) => {
        console.error(`❌ WebSocket error with ${agent.name}:`, error);
        reject(error);
      });

      // Timeout de connexion
      setTimeout(() => {
        if (ws.readyState !== WebSocket.OPEN) {
          ws.close();
          reject(new Error('WebSocket connection timeout'));
        }
      }, 10000);
    });
  }

  /**
   * Gère les messages reçus des agents
   */
  private handleAgentMessage(agentId: string, message: AgentMessage): void {
    const agent = this.agents.get(agentId);
    if (!agent) return;

    // Mettre à jour le heartbeat
    agent.lastHeartbeat = new Date();
    this.agents.set(agentId, agent);

    // Émettre le message pour les interfaces
    this.emit('agent:message', {
      agentId,
      agent,
      message
    });

    // Traitement spécifique selon le type de message
    switch (message.type) {
      case 'HEARTBEAT':
        this.emit('agent:heartbeat', { agentId, agent });
        break;
        
      case 'STATUS_UPDATE':
        this.emit('agent:status-update', { agentId, agent, data: message.data });
        break;
        
      case 'CAPABILITY_UPDATE':
        agent.capabilities = message.data.capabilities || agent.capabilities;
        this.agents.set(agentId, agent);
        this.emit('agent:capability-update', { agentId, agent });
        break;
        
      case 'ERROR':
        this.emit('agent:error', { agentId, agent, error: message.data });
        break;
        
      default:
        this.emit('agent:custom-message', { agentId, agent, message });
    }
  }

  /**
   * Envoie un message à un agent spécifique
   */
  public async sendToAgent(agentId: string, message: any): Promise<AgentResponse> {
    const agent = this.agents.get(agentId);
    if (!agent) {
      throw new Error(`Agent ${agentId} not found`);
    }

    const ws = this.wsConnections.get(agentId);
    if (ws && ws.readyState === WebSocket.OPEN) {
      // Envoi via WebSocket
      return new Promise((resolve, reject) => {
        const correlationId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        const messageWithId = {
          ...message,
          correlationId,
          timestamp: Date.now(),
          source: 'hanuman-neural-network'
        };

        // Écouter la réponse
        const responseHandler = (data: any) => {
          if (data.agentId === agentId && data.message.correlationId === correlationId) {
            this.off('agent:message', responseHandler);
            resolve({
              success: true,
              data: data.message.data,
              timestamp: Date.now(),
              agentId
            });
          }
        };

        this.on('agent:message', responseHandler);

        // Timeout
        setTimeout(() => {
          this.off('agent:message', responseHandler);
          reject(new Error('Message timeout'));
        }, 30000);

        ws.send(JSON.stringify(messageWithId));
      });
    } else {
      // Fallback vers HTTP
      try {
        const url = `http://${agent.host}:${agent.port}${agent.apiPath}/message`;
        const response = await axios.post(url, message, { timeout: 30000 });
        
        return {
          success: true,
          data: response.data,
          timestamp: Date.now(),
          agentId
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: Date.now(),
          agentId
        };
      }
    }
  }

  /**
   * Diffuse un message à tous les agents actifs
   */
  public async broadcastToAllAgents(message: any): Promise<Map<string, AgentResponse>> {
    const responses = new Map<string, AgentResponse>();
    const activeAgents = Array.from(this.agents.values()).filter(agent => agent.status === 'active');

    const promises = activeAgents.map(async (agent) => {
      try {
        const response = await this.sendToAgent(agent.id, message);
        responses.set(agent.id, response);
      } catch (error) {
        responses.set(agent.id, {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: Date.now(),
          agentId: agent.id
        });
      }
    });

    await Promise.allSettled(promises);
    return responses;
  }

  /**
   * Vérifie la santé de tous les agents
   */
  private async checkAllAgentsHealth(): Promise<void> {
    const healthPromises = Array.from(this.agents.values()).map(async (agent) => {
      if (agent.status === 'active') {
        const isHealthy = await this.performHealthCheck(agent);
        if (!isHealthy) {
          agent.status = 'error';
          this.agents.set(agent.id, agent);
          this.emit('agent:health-check-failed', agent);
        }
      }
    });

    await Promise.allSettled(healthPromises);
  }

  /**
   * Programme une reconnexion pour un agent
   */
  private scheduleReconnection(agentId: string): void {
    const attempts = this.reconnectAttempts.get(agentId) || 0;
    if (attempts >= this.maxReconnectAttempts) {
      console.log(`❌ Max reconnection attempts reached for ${agentId}`);
      return;
    }

    const delay = Math.min(1000 * Math.pow(2, attempts), 30000); // Exponential backoff, max 30s
    
    setTimeout(async () => {
      console.log(`🔄 Attempting to reconnect to ${agentId} (attempt ${attempts + 1})`);
      const success = await this.connectToAgent(agentId);
      
      if (success) {
        this.reconnectAttempts.delete(agentId);
      } else {
        this.reconnectAttempts.set(agentId, attempts + 1);
      }
    }, delay);
  }

  /**
   * Obtient le statut de tous les agents
   */
  public getAllAgentsStatus(): AgentConfig[] {
    return Array.from(this.agents.values());
  }

  /**
   * Obtient le statut d'un agent spécifique
   */
  public getAgentStatus(agentId: string): AgentConfig | undefined {
    return this.agents.get(agentId);
  }

  /**
   * Obtient les agents par type
   */
  public getAgentsByType(type: AgentConfig['type']): AgentConfig[] {
    return Array.from(this.agents.values()).filter(agent => agent.type === type);
  }

  /**
   * Obtient les agents actifs
   */
  public getActiveAgents(): AgentConfig[] {
    return Array.from(this.agents.values()).filter(agent => agent.status === 'active');
  }

  /**
   * Ferme toutes les connexions
   */
  public async disconnect(): Promise<void> {
    // Fermer toutes les connexions WebSocket
    this.wsConnections.forEach((ws, agentId) => {
      ws.close();
    });
    this.wsConnections.clear();

    // Arrêter tous les intervalles de heartbeat
    this.heartbeatIntervals.forEach((interval) => {
      clearInterval(interval);
    });
    this.heartbeatIntervals.clear();

    // Mettre à jour le statut de tous les agents
    this.agents.forEach((agent, agentId) => {
      agent.status = 'inactive';
      this.agents.set(agentId, agent);
    });

    console.log('🔌 Disconnected from all agents');
  }
}

// Instance singleton pour l'utilisation globale
export const agentConnectionManager = new AgentConnectionManager();
